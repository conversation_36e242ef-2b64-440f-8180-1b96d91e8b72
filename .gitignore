# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# block npm file, since we use yarn
package-lock.json

# testing
/coverage
/cypress/videos
/cypress/screenshots
/cypress/reports
/src/coverage
/src/cypress/videos
/src/cypress/screenshots
/src/cypress/reports

# production
/build
/dist

# misc
.idea
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
/storybook-static/

# crt and key used for https on localhost
localhost.crt
localhost.key
localhost+2-key.pem
localhost+2.pem