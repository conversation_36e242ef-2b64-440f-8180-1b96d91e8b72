variables:
  DOCKER_AUTH_CONFIG: $CI_DOCKER_CHUPACABRA_AUTH
  APP_VERSION: ""
  FEATURE_MAX_DAYS: 14
  E2E_TESTS_IMAGE: cypress/browsers:node-18.14.1-chrome-111.0.5563.146-1-ff-111.0.1-edge-111.0.1661.54-1
  DOCKER_WEB_BUILDER_IMAGE: registry.opl.tools/rumiuk/tools/docker-base/tvgo_web_builder:22.14.0
  
image:
  name: $DOCKER_WEB_BUILDER_IMAGE

cache:
  paths:
    - .yarn
    - node_modules/
    - /root/.cache/Cypress

stages:
  - dependencies-audit
  - pre-tests
  - build
  - deploy
  - publish
  - post-tests

before_script:
  - export APP_VERSION=$(cat package.json | grep version | head -1 | awk -F= "{ print $2 }" | sed 's/[version:,\",]//g' | tr -d [:space:])

include:
  - "ci-scripts/yarn-audit.yml"
  - "ci-scripts/unit-tests.yml"
  - "ci-scripts/get-default-tech-config.yml"
  - "ci-scripts/e2e-chrome.yml"
  - "ci-scripts/pipelines/feature.yml"
  - "ci-scripts/pipelines/dev.yml"
  - "ci-scripts/pipelines/dev-e2e.yml"
  - "ci-scripts/pipelines/preprod.yml"
  - "ci-scripts/pipelines/bis.yml"
  - "ci-scripts/pipelines/prod.yml"