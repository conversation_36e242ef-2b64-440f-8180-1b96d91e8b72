# Orange TV Go application for WEB

## Table of Contents
1. [Introduction](#introduction)
3. [Environments](#environments)
2. [Prerequisites](#prerequisites)
3. [Installation](#installation)
4. [Running the Application](#running-the-application)
5. [Building the Application](#building-the-application)
6. [Deployment](#deployment)
7. [Contribution](#contribution)
8. [Storybook](#storybook)
9. [Style Guide](#style-guide)


## Introduction
Welcome to the Orange TV Go. The web app provides access to live TV channels, programs and on-demand videos. Additional information about project are available on [Confluence](https://scportal.corp.tepenet/confluence/display/YPT/WEB_V2).

## Environments
### Production https://tvgo.orange.pl/
- Production environment used by application clients. Includes an application built on top of the production branch.
### Bis-Production https://tvgo.bis.orange.pl/
- Bis-Production environment used for tests that should mirror production. Sometimes used for development due to greater availability and stability of external services. Includes an application built on top of the master branch.
### Pre-production https://tvgo.ppd.orange.pl/ and https://tvgo-dev.chupacabra.tp.pl/
- https://tvgo.ppd.orange.pl/ - Pre-production environment used to tests and development. Includes an application built on top of the master branch. (FE PPD + BE PPD) Need connection by lab VPN.
- https://tvgo-dev.chupacabra.tp.pl/ - Pre-production environment used primarily by [E2E tests written in cypress](https://git.opl.tools/rumiuk/atvapps/orange-tv-go-web-automatic-tests). Includes an application built on top of the develop branch (FE DEV + BE PPD). Need connection by lab VPN.
### Develop https://tvgo-dev2.chupacabra.tp.pl/
- Development environment used for development and tests of application. Includes an application built on top of the develop branch (FE DEV + BE DEV). Need connection by lab VPN.

## Prerequisites
Before you can start working with the Orange TV Go, ensure you have the following installed on your system:
1. Node.js v22.0.0 or later: [Download Node.js](https://nodejs.org/en/download/)
2. Yarn v1.22.21 or later: [Download Yarn](https://classic.yarnpkg.com/en/docs/getting-started)
3. Git: Download Git: [Download Git](https://git-scm.com/downloads)

## Installation
Follow these steps to install the project dependencies and set up the Orange TV Go on your local machine:
1. Clone the repository to current folder
```bash
<NAME_EMAIL>:rumiuk/atvapps/ypt-frontend-2.git .
```
2. Install the project dependencies by running:
```bash
yarn install
```

## Running the Application
Follow these steps to run Orange TV Go locally. Depending selected environment you might need to have VPN connection with lab network (PPD and DEV).
1. Start VPN connection
2. Go to the root level of project in console
3. To use localhost over https generate `localhost.crt` and `localhost.key`
```bash
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout localhost.key -out localhost.crt
```
handling https is defined in `vite.config.ts` by
```
https: {
  key: fs.readFileSync(path.resolve(__dirname, 'localhost.key')),
  cert: fs.readFileSync(path.resolve(__dirname, 'localhost.crt')),
},
```

4. Start application by running:
```bash
yarn start
```
5. This will start the development server on port 3000 and automatically open your default web browser to `https://localhost:3000`.


By default application is running with .env.proxy setup which use pre-production BE. For other scripts go to package.json file

### To run app locally on different BE environment, follow this steps:
1. Change server.proxy.target in vite.config.ts eg. https://tvgo.bis.orange.pl
2. Update .env.proxy to use bis endpoints eg. VITE_REACT_APP_ASSET_URL=https://tvgo.bis.orange.pl
3. For bis-production change VITE_REACT_APP_USE_PPD_DRM_SERVER to false if you need player
4. For bis-production change globalConfig.api.deviceType to android_mobile_otf (It bypass API fingerprint protection)
5. Start application by running:
```bash
yarn start
```

## Testing
Orange TV Go uses various tools and libraries to ensure that the codebase is reliable, high-quality, and easy to maintain. These include:

- Vitest with React Testing Library for unit and integration testing
- MSW for mocking API requests
- [Cypress for end-to-end testing](https://git.opl.tools/rumiuk/atvapps/orange-tv-go-web-automatic-tests)

## Building the Application
In Orange TV Go, we use Vite to build the application. Vite is a build tool that provides fast, efficient building and development experiences for modern web projects. It supports features like hot module replacement, optimized bundling, and tree-shaking.

To build the application locally based on development environment, use:
```bash
build:dev
```

### Create fresh application package for environment
#### Bis-production and Pre-production
1. Create new merge request where target branch is master and source branch is develop
2. Check if app version in package.json is correct and all changes are included in MR
3. After pipeline success you can merge
4. Package now should be available on https://apk.chupacabra.tp.pl/web2

#### Production
1. Create new merge request where target branch is production and source branch is master
2. Check if app version in package.json is correct and all changes are included in MR
3. After pipeline success you can merge
4. Package now should be available on https://apk.chupacabra.tp.pl/web2


## Contribution
### New feature or bugfix
1. Assign a task to yourself on JIRA
2. Create a new branch according to the rules from [Style Guide](#style-guide) based on the latest develop branch
3. Make changes
4. Create a commit with the changes
5. Push commits to your branch
6. Create a new merge request where target is develop and source is the name of your branch
7. CI/CD should starts automatically, after build and deploy, your changes should be available on https://tvgo-dev2.chupacabra.tp.pl/YPT-XXXXX/
8. Set label as ready for code review
9. After code review and fixes, change label to ready for tests
10. New features are merged to target branch by testers after tests

### Hotfix
1. Assign a task to yourself on JIRA
2. Depending if hotfix is for production or bis create new branch based on production or master branch
3. Follow steps same as in New feature or bugfix
4. After hotfix tests and merge, propagate changes to lower levels branches

### Propagate hotfix
1. If hotfix was on production go to master branch
2. Pull production to master branch
3. Push master to remote repository
4. Go to develop
5. Pull master to develop branch
6. Push develop to remote repository

## Deployment
The entire process of building and deploying the Orange TV Go application is defined in the .gitlab-ci.yml file the relevant scripts run automatically depending on which branch the changes occurred on. 

## Storybook 
In Orange TV Go, we use Storybook to develop and test UI components in isolation. We can build and preview components in a sandboxed environment, and then easily integrate them into the application. [Storybook](https://tvgo-sb.chupacabra.tp.pl/) is automatically deployed while building and deploying new develop.


## Style Guide
### Gitlab
**Each merge request should have proper naming**
- Format `Type/Task: Description`
  - Type `'Feature' | 'Task' | 'Fix'`
  - Task `Jira task number` 
  - Description `Task description in english`
- Example `Feature/YPT-10887: Logout functionality`
​

**Each merge request should have at least 2 main approvals before merging**
- approvals should be given from one of `development team` members
​
### Code writing rules 
**Name shortcuts**
- We should not use any variable shortcuts and assign descriptive names 
  - :confused: `str, prevValue`
  - :blush: `string, previousValue`
- There are few exceptions
  - :blush: `S` for styles imported as styled components
    - `import * as S from './styles`
  - :blush: project specific names
    - `vod`
    - `npvr`


**Component creation**
- We should follow only one pattern when writing components
  - :confused: `function ComponentName() {return ()}`
  - :blush: `const ComponentName = () => {return ()}`
- We don't want `JSX.Element` as component return type
  - :confused: `const ComponentName = (): JSX.Element => {return <></>}`
  - :blush: `const ComponentName = () => {return (<></>)}`


**Constant value**
- All constants values should be defined in `constants.ts` file, even if there is only one 
- Constants should be all capital letters with ‘_’ separators
  - :confused: `superTime`
  - :blush: `SUPER_TIME`


**Boolean props names**
- Boolean props names should start with `is`, `has`, `should`, etc. prefix
  - :confused: `active`
  - :blush: `isActive`
​

**Boolean props position**
- We keep boolean props in the end of props list
  - :confused: `<Component isOpen={isOpen} keepMounted handleClose={handleClose}>`
  - :blush: `<Component isOpen={isOpen} handleClose={handleClose} keepMounted>`


**Boolean casting**
- Boolean should be casted with `Boolean()` method
  - :confused: `!!value`
  - :blush: `Boolean(value)`
​

**Conditional rendering**
- For conditional rendering based on variable, which is not defined as boolean, we use casting by `Boolean()` and return `null`
  - :confused: `isBoolean && null`
  - :blush: `Boolean(isBoolean) && null`


**Props destructuring**
- If there is a possibility to destructure object, let's do it
  - :confused: `const Component = (props) => {}`
  - :blush: `const Component = ({ id }) => {}`


**Unused params**
- We should follow only one pattern when writing unused params
  - :confused: `onError: (error, abc, mutation => {}`
  - :blush: `onError: (error, _unusedA, _unusedB, ..., mutation => {}`
​

**Array type declarations**
- We should follow only one pattern when writing types
  - :confused: `someArray: string[]`
  - :blush: `someArray: Array<string>`
​

**React imports**
- We should never not import `React from 'react'`
  - :confused: `React.useEffect(() => {})`
  - :blush: `useEffect(() => {})`
​

**One liners**
- We don't want one liners
  - :confused: `if (something) return;`
  - :blush:
    ```
    if (something) {
      return;
    }
    ```

**If statements**
- If possible we want to avoid falsy statement
  - :confused:
    ```
    if (!something) {
      doSomething();
    }
    return;
    ```
  - :blush:
    ```
    if (something) {
      return;
    }
    doSomething();
    ```
- We don't want nested `if` statements
  - :confused:
      ```
      if (something) {
        if (somethingElse) {
          doSomethingElse();
        }
      }
      return;
      ```
  - :blush:
      ```
      const doSomething = () => {
        if (somethingElse) {
          doSomethingElse()
        }
      }

      if (something) {
        doSomething();
      }
      ```

**Translations**
- We should always use `react-intl` package for entered text
  - :confused: `<Text>Some Title</Text>`
  - :blush: `<Text>{intl.formatMessage(messages.title)}</Text>`
​

**Framer Motion**
- Framer motion states should be defined inside `styles`


**Installed packages**
- We should use only strict versions of installed packages
  - :confused: `"react-dom": "^17.0.2"`
  - :blush: `"react-dom": "17.0.2"`
- We should keep installed packages in correct structure
  - :confused: "dependencies": { "eslint-plugin-prettier": "^4.0.0" }
  - :blush: "devDependencies": { "eslint-plugin-prettier": "4.0.0" }

