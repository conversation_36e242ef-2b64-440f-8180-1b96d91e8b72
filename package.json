{"name": "ypt_frontend_2", "version": "1.18.0", "type": "module", "private": true, "engines": {"node": "22.14.0", "npm": "not-supported"}, "scripts": {"start": "yarn start:proxy", "start:proxy": "vite --mode proxy", "start:mock": "vite --mode mock", "start:feature": "vite --mode feature", "start:dev": "vite --mode dev", "start:dev-e2e": "vite --mode dev-e2e", "start:bis": "vite --mode bis", "start:preprod": "vite --mode preprod", "start:prod": "vite --mode prod", "start:storybook": "env-cmd -f .env.proxy storybook dev -p 6006", "build:feature": "vite build --mode feature", "build:dev": "vite build --mode dev", "build:dev-e2e": "vite build --mode dev-e2e", "build:bis": "vite build --mode bis", "build:preprod": "vite build --mode preprod", "build:prod": "vite build --mode prod", "build:storybook": "env-cmd -f .env.dev storybook build", "test": "vitest --mode dev --run ", "test-watch": "vitest --mode dev --run --watch", "test-coverage": "yarn test --coverage", "lint": "yarn lint:js && yarn lint:types && yarn check:circular-deps && yarn lint:prettier", "lint:audit": "./ci-scripts/audit-custom.sh", "lint:css": "stylelint ./**/*.{ts,tsx}", "lint:js": "eslint --ext ts,tsx src/", "lint:types": "tsc --noEmit --project tsconfig.lint.json", "lint:prettier": "prettier --check src", "prettier": "prettier --write src", "check:circular-deps": "madge --circular src", "eject": "react-scripts eject", "prepare": "husky install", "endpoints": "ts-node --project ./node.tsconfig.json ./src/scripts/endpointsReader/endpointsReader.ts"}, "dependencies": {"axios": "1.6.2", "browserslist-to-esbuild": "2.1.1", "crypto-js": "4.2.0", "date-fns": "3.0.6", "embla-carousel-react": "7.1.0", "embla-carousel-wheel-gestures": "3.0.0", "firebase": "10.1.0", "framer-motion": "10.16.16", "idb": "8.0.0", "prop-types": "15.7.2", "react": "18.2.0", "react-cookie": "6.1.1", "react-datepicker": "8.0.0", "react-dom": "18.2.0", "react-hook-form": "7.39.2", "react-intl": "6.5.5", "react-query": "3.39.2", "react-router-dom": "6.4.3", "react-select": "5.8.0", "react-toastify": "9.1.1", "styled-components": "6.1.8", "styled-reset": "4.4.2", "ua-parser-js": "1.0.37", "url-join": "5.0.0", "use-debounce": "10.0.0", "voplayer-html5": "file:./external/voplayer-html5"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "7.21.11", "@faker-js/faker": "7.6.0", "@storybook/addon-a11y": "7.6.5", "@storybook/addon-actions": "7.6.5", "@storybook/addon-controls": "7.6.5", "@storybook/addon-essentials": "7.6.5", "@storybook/addon-links": "7.6.5", "@storybook/addon-storyshots": "7.6.5", "@storybook/addon-storysource": "7.6.5", "@storybook/addons": "7.6.5", "@storybook/node-logger": "7.6.5", "@storybook/preset-create-react-app": "7.6.5", "@storybook/react": "7.6.5", "@storybook/react-vite": "8.0.4", "@storybook/theming": "7.6.5", "@testing-library/jest-dom": "6.4.2", "@testing-library/react": "14.1.2", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "14.5.1", "@types/crypto-js": "4.1.1", "@types/node": "22.0.0", "@types/react": "18.2.45", "@types/react-dom": "18.0.8", "@types/react-router-dom": "5.1.7", "@types/react-select": "4.0.17", "@types/styled-components": "5.1.34", "@types/ua-parser-js": "0.7.36", "@typescript-eslint/eslint-plugin": "6.16.0", "@typescript-eslint/parser": "6.16.0", "@vitejs/plugin-react": "4.2.1", "babel-plugin-styled-components": "2.1.4", "customize-cra": "1.0.0", "dotenv": "16.4.5", "env-cmd": "10.1.0", "eslint": "8.57.0", "eslint-config-prettier": "8.5.0", "eslint-config-react-app": "7.0.1", "eslint-import-resolver-typescript": "3.5.2", "eslint-plugin-prettier": "5.0.1", "eslint-plugin-storybook": "0.6.15", "fake-indexeddb": "5.0.1", "husky": "8.0.2", "jsdom": "24.0.0", "lint-staged": "15.2.0", "madge": "6.1.0", "msw": "1.3.2", "msw-storybook-addon": "1.10.0", "prettier": "3.1.0", "start-server-and-test": "2.0.3", "storybook": "7.6.5", "stylelint": "16.0.2", "stylelint-config-prettier": "9.0.4", "stylelint-config-recommended": "14.0.0", "stylelint-config-standard": "36.0.0", "stylelint-config-styled-components": "0.1.1", "ts-node": "10.9.1", "tsconfig-paths": "4.1.0", "tsconfig-paths-webpack-plugin": "4.0.0", "typescript": "5.1.6", "vite": "5.1.6", "vite-tsconfig-paths": "4.3.2", "vitest": "1.4.0"}, "lint-staged": {"**/*.{ts,tsx}": ["eslint", "prettier --check"]}, "msw": {"workerDirectory": "public"}}