import { Loader } from 'components/Loader';
import { Slider } from 'components/Slider';
import { Channel } from 'components/Channel';
import { H2 } from 'components/Typography';

import * as S from './styles';
import { ChannelsGridProps } from './types';

export const ChannelsGrid = ({
  channels,
  isFetching,
  renderHeader,
  title,
  isSlider,
  noDataMessage,
}: ChannelsGridProps) => {
  const showNoDataMessage = channels.length === 0;

  if (showNoDataMessage) {
    return (
      <S.Container>
        {title && <H2>{title}</H2>}
        <S.NoDataInfo>{noDataMessage}</S.NoDataInfo>
      </S.Container>
    );
  }

  const slides = channels?.map((channel) => (
    <S.SlideContainer key={`channelsGrid-${channel?.channelExtId}`}>
      {Boolean(channel) && <Channel channel={channel} />}
    </S.SlideContainer>
  ));

  return (
    <S.Container>
      {title ? <H2>{title}</H2> : renderHeader && renderHeader()}
      {isFetching ? (
        <Loader />
      ) : (
        <>
          {!isSlider ? (
            <S.Grid>{slides}</S.Grid>
          ) : (
            <Slider align='start' withButtons dragFree>
              {slides}
            </Slider>
          )}
        </>
      )}
    </S.Container>
  );
};
