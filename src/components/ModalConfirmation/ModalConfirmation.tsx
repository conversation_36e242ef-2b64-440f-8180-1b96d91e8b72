import { createPortal } from 'react-dom';

import { Modal, ModalProps } from 'components/Modal';
import { Text } from 'components/Typography';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { Image } from 'components/Image';
import { getPublicAssetUrl } from 'utils/url';

import { ModalConfirmationProps } from './types';
import * as S from './styles';

export const ModalConfirmation = ({
  isOpen,
  onClose,
  modalTitle,
  buttonSubmitText,
  buttonDeniedText,
  onSubmit,
  onDenied,
  modalDescription,
  modalDescriptionType = 'wordingVal',
  withSubmitClose = true,
  children,
}: Pick<ModalProps, 'isOpen' | 'onClose'> & ModalConfirmationProps) => {
  const handleSubmit = () => {
    onSubmit && onSubmit();
    if (withSubmitClose) {
      onClose && onClose();
    }
  };

  const handleDenied = () => {
    onDenied && onDenied();
    onClose && onClose();
  };

  const modalImage = (
    <S.ImageContent>
      <Image
        src={getPublicAssetUrl(modalDescription || '')}
        photoRatio={9 / 16}
      />
    </S.ImageContent>
  );

  const modalText = (
    <S.Content>
      {modalDescription?.split('\n').map((string, index) => (
        <Text key={index} $secondary $sizeSmall>
          {string}
        </Text>
      ))}
    </S.Content>
  );

  const modalMultilineText = (
    <S.MultilineContent>
      {modalDescription?.split('\n').map((string, index) => (
        <Text key={index} $secondary $sizeSmall>
          {string}
        </Text>
      ))}
    </S.MultilineContent>
  );

  const getProperModalDescription = () => {
    if (modalDescriptionType === 'image') {
      return modalImage;
    }

    if (modalDescriptionType === 'multiline') {
      return modalMultilineText;
    }

    return modalText;
  };

  return (
    <>
      {createPortal(
        <Modal title={modalTitle} isOpen={isOpen} onClose={onClose}>
          <S.Container>
            {getProperModalDescription()}
            {children}
            {(onSubmit || onDenied) && (
              <S.ButtonsWrapper>
                {buttonSubmitText && (
                  <PrimaryButton
                    variant='orange'
                    onClick={handleSubmit}
                    dataTestId='modalSubmit'
                  >
                    <Text>{buttonSubmitText}</Text>
                  </PrimaryButton>
                )}
                {buttonDeniedText && (
                  <PrimaryButton variant='ghostWhite' onClick={handleDenied}>
                    <Text>{buttonDeniedText}</Text>
                  </PrimaryButton>
                )}
              </S.ButtonsWrapper>
            )}
          </S.Container>
        </Modal>,
        document.getElementById('appShell') || document.body,
      )}
    </>
  );
};
