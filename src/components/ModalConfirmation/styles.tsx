import styled from 'styled-components';

export const ButtonsWrapper = styled.div`
  display: flex;
  justify-content: right;
  padding-top: 3.2rem;
  & > button:last-child {
    margin-left: 3.2rem;
  }
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 70rem;
`;

export const Content = styled.div`
  padding-top: 3.2rem;

  /* Każdy Text komponent w nowej linii */
  & > span {
    display: block;
    margin-bottom: 0.8rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

export const ImageContent = styled(Content)``;

ButtonsWrapper.displayName = 'ModalConfirmationButtonsWrapper';
Container.displayName = 'LoginModalContainer';
Content.displayName = 'ModalConfirmationContent';
