import { ContentType } from 'services/api/newApi/core/appConfigs';

// Rozszerzamy ContentType o 'multiline' dla tekstów z nowymi liniami
export type ModalDescriptionType = ContentType | 'multiline';

export interface ModalConfirmationProps {
  modalTitle?: string;
  modalDescription?: string;
  modalDescriptionType?: ModalDescriptionType;
  buttonSubmitText?: string;
  buttonDeniedText?: string;
  onSubmit?: () => void;
  onDenied?: () => void;
  withSubmitClose?: boolean;
  children?: JSX.Element;
}
