import { useIntl } from 'react-intl';

import { ModalConfirmation } from 'components/ModalConfirmation';

import { messages } from './messages';
import { UserMsgPopupProps } from './types';

export const UserMsgPopup = ({
  isOpen,
  onClose,
  userMsg,
}: UserMsgPopupProps) => {
  const { formatMessage } = useIntl();

  return (
    <ModalConfirmation
      modalDescription={userMsg}
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onClose}
      buttonSubmitText={formatMessage(messages.okButton)}
    />
  );
};
