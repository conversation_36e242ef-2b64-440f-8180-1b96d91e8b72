import { useIntl } from 'react-intl';

import { ModalConfirmation } from 'components/ModalConfirmation';

import { messages } from './messages';
import { UserMsgPopupProps } from './types';

export const UserMsgPopup = ({
  isOpen,
  onClose,
  userMsg,
}: UserMsgPopupProps) => {
  const { formatMessage } = useIntl();

  const lines = userMsg.split('\n');
  const modalTitle = lines[0] || '';
  const modalDescription = lines.slice(1).join('\n') || '';

  return (
    <ModalConfirmation
      modalTitle={modalTitle}
      modalDescription={modalDescription}
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onClose}
      buttonSubmitText={formatMessage(messages.okButton)}
    />
  );
};
