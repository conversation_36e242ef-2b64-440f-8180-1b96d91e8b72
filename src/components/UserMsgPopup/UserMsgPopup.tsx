import { useIntl } from 'react-intl';

import { ModalConfirmation } from 'components/ModalConfirmation';

import { messages } from './messages';
import { UserMsgPopupProps } from './types';

export const UserMsgPopup = ({
  isOpen,
  onClose,
  userMsg,
}: UserMsgPopupProps) => {
  const { formatMessage } = useIntl();

  // Fix dla przypadku gdy backend zwraca \\n zamiast \n
  const processedUserMsg = userMsg
    .replace(/\\n/g, '\n') // Zamień \\n na \n
    .replace(/\\r\\n/g, '\n') // Zamień \\r\\n na \n
    .replace(/\\r/g, '\n'); // Zamień \\r na \n

  // Debug - usuń po sprawdzeniu
  console.log('Original userMsg:', JSON.stringify(userMsg));
  console.log('Processed userMsg:', JSON.stringify(processedUserMsg));

  return (
    <ModalConfirmation
      modalDescription={processedUserMsg}
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onClose}
      buttonSubmitText={formatMessage(messages.okButton)}
    />
  );
};
