import { useIntl } from 'react-intl';
import styled from 'styled-components';

import { ModalConfirmation } from 'components/ModalConfirmation';
import { Text } from 'components/Typography';

import { messages } from './messages';
import { UserMsgPopupProps } from './types';

// Styled komponent dla tekstu z obsługą nowych linii
const UserMessageContent = styled.div`
  padding-top: 3.2rem;

  & > span {
    display: block;
    margin-bottom: 0.8rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

export const UserMsgPopup = ({
  isOpen,
  onClose,
  userMsg,
}: UserMsgPopupProps) => {
  const { formatMessage } = useIntl();

  // Fix dla przypadku gdy backend zwraca \\n zamiast \n
  const processedUserMsg = userMsg
    .replace(/\\n/g, '\n') // Zamień \\n na \n
    .replace(/\\r\\n/g, '\n') // Zamień \\r\\n na \n
    .replace(/\\r/g, '\n'); // Zamień \\r na \n

  // Własny content z obsługą nowych linii
  const userMessageContent = (
    <UserMessageContent>
      {processedUserMsg.split('\n').map((line, index) => (
        <Text key={index} $secondary $sizeSmall>
          {line}
        </Text>
      ))}
    </UserMessageContent>
  );

  return (
    <ModalConfirmation
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onClose}
      buttonSubmitText={formatMessage(messages.okButton)}
    >
      {userMessageContent}
    </ModalConfirmation>
  );
};
