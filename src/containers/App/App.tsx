import { FC, useEffect } from 'react';

import { LazyComponentLoader } from 'components/LazyComponentLoader';
import { AppProviders } from 'containers/App/providers';
import { PlayerWrapper } from 'features/Player';
import { useRandomFeedbackToast } from 'features/Settings/Feedback';
import { Routes } from 'routes';
import { ApplicationThemeProvider } from 'theme';
import { AppLayoutModeProvider } from 'services/appLayoutMode';
import { ConsoleLogger } from 'services/logger';
import { CustomToastContainer } from 'components/Toast';
import { UserThemeProvider } from 'theme/UserThemeContext';
import { useUserSeamlessMigration } from 'services/user/UserSeamlessMigration';
import { useUserSession } from 'services/user';

import { Container, InnerContainer } from './styles';
import { usePlayChannelViaConsole } from './hooks/usePlayChannelViaConsole';
import { useSystemReady } from './hooks/useSystemReady';

const GlobalUtilities = () => {
  usePlayChannelViaConsole();
  return null;
};

const AppShell: FC<{ needLogout: boolean; setLogoutCompleted: () => void }> = ({
  needLogout,
  setLogoutCompleted,
}) => {
  useRandomFeedbackToast();

  const { logout } = useUserSession();
  const { isLoading: isSystemLoading } = useSystemReady();

  useEffect(() => {
    if (needLogout) {
      logout();
      setLogoutCompleted();
    }
  }, [logout, needLogout, setLogoutCompleted]);

  if (isSystemLoading) {
    return <LazyComponentLoader />;
  }

  return (
    <div id='appShell'>
      <PlayerWrapper>
        <InnerContainer>
          <GlobalUtilities />
          <Routes />
        </InnerContainer>
      </PlayerWrapper>
    </div>
  );
};

export const App: FC = () => {
  const logger = new ConsoleLogger();
  const { isAuthMigrationChecking, needLogout, setLogoutCompleted } =
    useUserSeamlessMigration();

  if (isAuthMigrationChecking) {
    return (
      <UserThemeProvider>
        <ApplicationThemeProvider>
          <LazyComponentLoader />
        </ApplicationThemeProvider>
      </UserThemeProvider>
    );
  }

  return (
    <UserThemeProvider>
      <ApplicationThemeProvider>
        <AppLayoutModeProvider>
          <Container data-testid='app-container'>
            <AppProviders logger={logger}>
              <AppShell
                needLogout={needLogout}
                setLogoutCompleted={setLogoutCompleted}
              />
              <CustomToastContainer />
            </AppProviders>
          </Container>
        </AppLayoutModeProvider>
      </ApplicationThemeProvider>
    </UserThemeProvider>
  );
};
