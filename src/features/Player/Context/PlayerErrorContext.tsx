import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useState,
} from 'react';

import {
  ErrorMessage,
  ErrorType,
  PlayerErrorScreenContextValue,
  PlayerErrorScreenProviderProps,
} from './types';

import { getErrorMessage } from '../PlayerErrorScreen/messages';

export const PlayerErrorScreenContext = createContext<
  PlayerErrorScreenContextValue | undefined
>(undefined);

export const PlayerErrorScreenProvider: FC<
  PropsWithChildren<PlayerErrorScreenProviderProps>
> = ({ children }) => {
  const [errorMessage, setErrorMessage] = useState<ErrorMessage>({
    message: '',
  });
  const [buttonHandler, setButtonHandler] = useState(() => () => {});

  const closeErrorScreen = useCallback(() => {
    setErrorMessage({ message: '' });
  }, []);

  const setupErrorHandler = useCallback(
    (errorType: ErrorType, errorCallback?: () => void) => {
      switch (errorType) {
        case 'CDN_ERROR':
          setButtonHandler(() => () => {
            closeErrorScreen();
            errorCallback && errorCallback();
          });
          break;
        case 'EXPIRED_ERROR':
          setButtonHandler(() => () => {
            closeErrorScreen();
            errorCallback && errorCallback();
          });
          break;
        default:
          break;
      }
    },
    [closeErrorScreen],
  );

  const showErrorScreen = useCallback(
    (errorType: ErrorType, errorCallback?: () => void) => {
      setErrorMessage(getErrorMessage(errorType));
      setupErrorHandler(errorType, errorCallback);
    },
    [setupErrorHandler],
  );

  return (
    <PlayerErrorScreenContext.Provider
      value={{
        showErrorScreen,
        closeErrorScreen,
        errorMessage,
        buttonHandler,
      }}
    >
      <div>{children}</div>
    </PlayerErrorScreenContext.Provider>
  );
};

export const usePlayerErrorScreen = () => {
  const context = useContext(PlayerErrorScreenContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerErrorScreenContext');
};
