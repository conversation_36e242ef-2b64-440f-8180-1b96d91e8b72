import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { AudioTrack, TextTrack } from 'voplayer-html5/lib/src/player/Types';

import {
  AudioOrSubsLanguage,
  useCoreHouseholdQuery,
} from 'services/api/newApi/core/household';

import {
  LanguagesAutoTrackOptions,
  PlayerInitialLanguagesContextValue,
  PlayerLanguagesProviderProps,
} from './types';

import { handleCodeLength } from '../Interface/SettingsMenu/Options/helpers/getValues';

const PlayerInitialLanguagesContext =
  createContext<PlayerInitialLanguagesContextValue>(
    {} as PlayerInitialLanguagesContextValue,
  );

export const PlayerInitialLanguagesProvider: FC<
  PropsWithChildren<PlayerLanguagesProviderProps>
> = ({ children }) => {
  const { data: householdInfo } = useCoreHouseholdQuery();

  const [initialAudioLanguage, setInitialAudioLanguage] =
    useState<AudioOrSubsLanguage | null>(null);
  const [initialSubtitles, setInitialSubtitles] =
    useState<AudioOrSubsLanguage | null>(null);

  const handleVideoWithProfileLanguage = useCallback(() => {
    if (householdInfo) {
      setInitialAudioLanguage(householdInfo.audioLang);
      setInitialSubtitles(householdInfo.subsLang);
      return;
    }
  }, [householdInfo]);

  const getInitialAutoTrack = useCallback(
    (
      playerTracks: AudioTrack[] | TextTrack[],
      type: LanguagesAutoTrackOptions,
    ) => {
      const initialLanguage = {
        [LanguagesAutoTrackOptions.AUDIO_LANGUAGE]: initialAudioLanguage,
        [LanguagesAutoTrackOptions.SUBTITLES_LANGUAGE]: initialSubtitles,
      };

      return playerTracks.findIndex(
        (track) =>
          handleCodeLength(track.language as AudioOrSubsLanguage) ===
          initialLanguage[type],
      );
    },
    [initialAudioLanguage, initialSubtitles],
  );

  return (
    <PlayerInitialLanguagesContext.Provider
      value={{
        handleVideoWithProfileLanguage,
        setInitialAudioLanguage,
        getInitialAutoTrack,
        initialAudioLanguage,
        initialSubtitles,
      }}
    >
      {children}
    </PlayerInitialLanguagesContext.Provider>
  );
};

export const usePlayerInitialLanguages =
  (): PlayerInitialLanguagesContextValue => {
    const context = useContext(PlayerInitialLanguagesContext);

    if (context) {
      return context;
    }

    throw new Error('Component beyond PlayerInitialLanguagesContext');
  };
