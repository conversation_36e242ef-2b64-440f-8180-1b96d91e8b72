import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useState,
} from 'react';

import {
  PlayerPlayback,
  PlayerPlaybackContextValue,
} from 'features/Player/Context/types';
import { PlayerType } from 'features/Player/types';
import { useGlobalLoaderContext } from 'services/loader';
import { useAppLayoutMode } from 'services/appLayoutMode';

import { usePlayerErrorScreen } from './PlayerErrorContext';

const PlayerPlaybackContext = createContext<PlayerPlaybackContextValue>(
  {} as PlayerPlaybackContextValue,
);

export const initialPlayback: PlayerPlayback = {
  isInitial: true,
};

export const PlayerPlaybackProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const [playback, setPlayback] = useState<PlayerPlayback>({
    ...initialPlayback,
  });

  const { setIsLoaderVisible } = useGlobalLoaderContext();
  const { resetLayoutMode } = useAppLayoutMode();
  const { closeErrorScreen } = usePlayerErrorScreen();

  const closePictureInPictureIfOpen = () => {
    document.pictureInPictureElement && document.exitPictureInPicture();
  };

  const reset = useCallback(() => {
    setIsLoaderVisible(false);
    setPlayback({ ...initialPlayback });
    resetLayoutMode();
    closeErrorScreen();
    closePictureInPictureIfOpen();
  }, [closeErrorScreen, resetLayoutMode, setIsLoaderVisible]);

  return (
    <PlayerPlaybackContext.Provider value={{ playback, setPlayback, reset }}>
      {children}
    </PlayerPlaybackContext.Provider>
  );
};

export const usePlayerPlayback = (): PlayerPlaybackContextValue => {
  const context = useContext(PlayerPlaybackContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerPlaybackContext');
};

export const usePlayerType = (): PlayerType => {
  const { playback } = usePlayerPlayback();

  return 'type' in playback ? playback.type : PlayerType.Vod;
};
