import { millisecondsToSeconds } from 'date-fns';
import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { ProgramId, TimeInMilliseconds } from 'services/api/common/types';
import {
  Channel,
  useChannelsAllQuery,
} from 'services/api/newApi/live/channels';
import {
  ProgramDetails,
  ScheduleProgram,
  useTvguideEpgQuery,
  useTvguideProgramQuery,
} from 'services/api/newApi/core/tvguide';
import { getCurrentDayStartDate, getDayStartDate } from 'utils/dateUtils';

import { LAG_TIME } from './constants';
import { PlayerProgramContextValue, PlayerProgramProviderProps } from './types';

const PlayerProgramContext = createContext<PlayerProgramContextValue>(
  {} as PlayerProgramContextValue,
);

export const PlayerProgramProvider: FC<
  PropsWithChildren<PlayerProgramProviderProps>
> = ({ channelId, programId = '', children }) => {
  const chosenDay = millisecondsToSeconds(getCurrentDayStartDate().getTime());
  const [currentProgramId, setCurrentProgramId] =
    useState<ProgramId>(programId);
  const [program, setProgram] = useState<ProgramDetails>({} as ProgramDetails);
  const { data: liveChannels } = useChannelsAllQuery();

  const { data: todaysEpg, isFetching: isFetchingTodaysEpg } =
    useTvguideEpgQuery({
      date: chosenDay,
    });

  const { data: currentProgramEpg } = useTvguideEpgQuery({
    date: getDayStartDate(program.endTimeUtc),
  });

  const { data: loadedProgram } = useTvguideProgramQuery(currentProgramId);

  const channel: Channel | undefined = useMemo(() => {
    return liveChannels?.find(
      (liveChannel) => liveChannel.channelExtId === channelId,
    );
  }, [channelId, liveChannels]);

  const isDynamicCatchup = useMemo(() => {
    const timeNow = millisecondsToSeconds(new Date().getTime());
    if (program.endTimeUtc) {
      return (
        timeNow > program.endTimeUtc && timeNow < program.endTimeUtc + LAG_TIME
      );
    }
    return false;
  }, [program.endTimeUtc]);

  const loadCurrentProgram = useCallback(
    (time: TimeInMilliseconds = Date.now()) => {
      const currentTime = millisecondsToSeconds(time);

      const channelSchedule = () => {
        return todaysEpg?.guide?.find(
          (schedule) => schedule.channelExtId === channelId,
        )?.programs;
      };

      const currentProgram = channelSchedule()?.find(
        (scheduleProgram) =>
          scheduleProgram.startTimeUtc <= currentTime &&
          scheduleProgram.endTimeUtc >= currentTime,
      );
      setCurrentProgramId(currentProgram?.programExtId || '');
    },
    [channelId, todaysEpg],
  );

  const loadProgram = useCallback(
    (programReferenceId: ProgramId) => {
      const channelSchedule = todaysEpg?.guide?.find(
        (schedule) => schedule.channelExtId === channelId,
      )?.programs;

      const currentProgram = channelSchedule?.find(
        (scheduleProgram) =>
          scheduleProgram.programExtId === programReferenceId,
      );

      setCurrentProgramId(currentProgram?.programExtId || '');
    },
    [channelId, todaysEpg],
  );

  const getNextElementFromSchedule = useCallback(
    (channelSchedule: Array<ScheduleProgram>) => {
      const currentProgramIndex = channelSchedule?.findIndex(
        (scheduleProgram) =>
          scheduleProgram.programExtId === program.programExtId,
      );
      const lastIndex = channelSchedule?.length - 1;
      const isNotLastElement = currentProgramIndex < lastIndex;

      if (isNotLastElement) {
        const nextProgramIndex = currentProgramIndex + 1;
        return channelSchedule[nextProgramIndex];
      }
      return null;
    },
    [program.programExtId],
  );

  const channelSchedule = useMemo(() => {
    return currentProgramEpg?.guide?.find(
      (schedule) => schedule.channelExtId === channelId,
    )?.programs;
  }, [channelId, currentProgramEpg]);

  const getNextProgram = useCallback(() => {
    if (currentProgramEpg) {
      return channelSchedule
        ? getNextElementFromSchedule(channelSchedule)
        : null;
    }
    return null;
  }, [currentProgramEpg, channelSchedule, getNextElementFromSchedule]);

  const getCurrentLiveProgram = useCallback(() => {
    if (currentProgramEpg) {
      const timeNow = millisecondsToSeconds(new Date().getTime());
      return (
        channelSchedule?.find((scheduleProgram) => {
          return (
            timeNow > scheduleProgram.startTimeUtc &&
            timeNow < scheduleProgram.endTimeUtc
          );
        }) || null
      );
    }
    return null;
  }, [channelSchedule, currentProgramEpg]);

  // get current program when specific program is not requested
  useEffect(() => {
    if (todaysEpg && !isFetchingTodaysEpg && !programId) {
      loadCurrentProgram();
    }
  }, [todaysEpg, isFetchingTodaysEpg, programId, loadCurrentProgram]);

  useEffect(() => {
    if (loadedProgram) {
      setProgram(loadedProgram);
    } else {
      setProgram({} as ProgramDetails);
    }
  }, [loadedProgram]);

  if (!channel) {
    return <></>;
  }

  return (
    <PlayerProgramContext.Provider
      value={{
        channel,
        program,
        isDynamicCatchup,
        loadProgram,
        loadCurrentProgram,
        getNextProgram,
        getCurrentLiveProgram,
      }}
    >
      {children}
    </PlayerProgramContext.Provider>
  );
};

export const usePlayerProgram = (): PlayerProgramContextValue => {
  const context = useContext(PlayerProgramContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerProgramContext');
};
