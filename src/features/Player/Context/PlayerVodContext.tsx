import { createContext, FC, PropsWithChildren, useContext } from 'react';

import { useVodAsset } from 'services/api/oldApi/vod';

import { PlayerVodContextValue, PlayerVodProviderProps } from './types';

const PlayerVodContext = createContext<PlayerVodContextValue>(
  {} as PlayerVodContextValue,
);

export const PlayerVodProvider: FC<
  PropsWithChildren<PlayerVodProviderProps>
> = ({ assetExternalId, children }) => {
  const { data: vod } = useVodAsset({ assetExternalId });

  if (!vod) {
    return <></>;
  }

  return (
    <PlayerVodContext.Provider value={{ vod }}>
      {children}
    </PlayerVodContext.Provider>
  );
};

export const usePlayerVod = (): PlayerVodContextValue => {
  const context = useContext(PlayerVodContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerVodContext');
};
