import { PlayerType } from 'features/Player/types';

import {
  CatchupPlayback,
  ChannelPlayback,
  InitialPlayback,
  PlayerPlayback,
  RecordingPlayback,
  StartoverPlayback,
  VodPlayback,
} from './types';

export const isInitialPlayback = (
  playback: PlayerPlayback,
): playback is InitialPlayback => {
  return 'isInitial' in playback;
};

export const isStartoverPlayback = (
  playback: PlayerPlayback,
): playback is StartoverPlayback => {
  return 'type' in playback && playback.type === PlayerType.Startover;
};

export const isCatchupPlayback = (
  playback: PlayerPlayback,
): playback is CatchupPlayback => {
  return 'type' in playback && playback.type === PlayerType.Catchup;
};

export const isChannelPlayback = (
  playback: PlayerPlayback,
): playback is ChannelPlayback => {
  return 'type' in playback && playback.type === PlayerType.Channel;
};

export const isVodPlayback = (
  playback: PlayerPlayback,
): playback is VodPlayback => {
  return 'type' in playback && playback.type === PlayerType.Vod;
};

export const isTrailerPlayback = (
  playback: PlayerPlayback,
): playback is VodPlayback => {
  return 'type' in playback && playback.type === PlayerType.Trailer;
};

export const isRecordingPlayback = (
  playback: PlayerPlayback,
): playback is RecordingPlayback => {
  return 'type' in playback && playback.type === PlayerType.Recording;
};

export const isChannelBasedPlayback = (
  playback: PlayerPlayback,
): playback is
  | ChannelPlayback
  | CatchupPlayback
  | StartoverPlayback
  | RecordingPlayback => {
  return (
    isStartoverPlayback(playback) ||
    isCatchupPlayback(playback) ||
    isRecordingPlayback(playback) ||
    isChannelPlayback(playback)
  );
};
