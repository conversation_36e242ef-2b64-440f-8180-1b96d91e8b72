import { vi, expect } from 'vitest';
import { useProgramTimer } from '../useProgramTimer';
import { renderHook } from '@testing-library/react';
import { usePlayerFeatures } from 'features/Player/Context';

vi.mock('features/Player/Context', () => ({
  usePlayerFeatures: vi.fn(() => ({
    features: {
      leadTime: 10,
      lagTime: 20,
      catchup: false,
      startOver: false,
    },
  })),
  usePlayerPlayback: () => ({ playback: null }),
  isInitialPlayback: () => true,
}));

describe('useProgramTimer', () => {
  it('should startPosition equal 0 for initialPlayback', () => {
    const { result } = renderHook(() =>
      useProgramTimer({ startTimeUtc: 100, endTimeUtc: 200 }),
    );
    expect(result.current.startPosition).toEqual(0);
  });

  it('should programBaseDuration equal only duration of content', () => {
    const { result } = renderHook(() =>
      useProgramTimer({ startTimeUtc: 100, endTimeUtc: 200 }),
    );
    expect(result.current.programBaseDuration).toEqual(100);
  });

  it('should programTotalDuration not include lead and lag time for live', () => {
    const { result } = renderHook(() =>
      useProgramTimer({ startTimeUtc: 100, endTimeUtc: 200 }),
    );
    expect(result.current.programTotalDuration).toEqual(100);
  });

  it('should programTotalDuration include lead and lag time for catchup', () => {
    const mockedUsePlayerFeatures = vi.mocked(usePlayerFeatures, true);
    mockedUsePlayerFeatures.mockReturnValue({
      features: {
        leadTime: 10,
        lagTime: 20,
        catchup: true,
        startOver: false,
        blackout: false,
        fastForward: false,
        npvr: false,
      },
    });
    const { result } = renderHook(() =>
      useProgramTimer({ startTimeUtc: 100, endTimeUtc: 200 }),
    );
    expect(result.current.programTotalDuration).toEqual(130);
  });
});
