import { useEffect, useState } from 'react';

import { usePlayerState } from 'features/Player/BasePlayer/Context';
import { usePlayerActions } from 'features/Player/BasePlayer/Hook';
import { usePlayerFeatures } from 'features/Player/Context';
import { PlayerState } from 'features/Player/types';
import { useLogger } from 'services/logger';
import { useUserProfile } from 'services/user';

export const useLivePauseWatcher = () => {
  const { logger } = useLogger();
  const { jumpToLive } = usePlayerActions();
  const { state } = usePlayerState();
  const {
    features: { startOver },
  } = usePlayerFeatures();
  const { isNpvrService } = useUserProfile();
  const [wasPaused, setPausedFlag] = useState<boolean>(false);

  useEffect(() => {
    switch (state) {
      case PlayerState.Pause:
        setPausedFlag(true);
        break;
      default: {
        if (wasPaused) {
          if (!startOver || !isNpvrService) {
            // when start over is disabled, we not allow to unpause in paused
            // position, instead we jump to live time
            logger.debug('Startover disabled, jump to live time after unpause');
            jumpToLive();
          }

          setPausedFlag(false);
        }
        break;
      }
    }
  }, [isNpvrService, jumpToLive, logger, startOver, state, wasPaused]);
};
