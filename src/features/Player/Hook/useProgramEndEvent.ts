import { useEffect, useMemo } from 'react';

import { usePlayerFeatures, usePlayerProgram } from 'features/Player/Context';
import { usePlayerTimer } from 'features/Player/BasePlayer/Context';
import { usePlayerActions } from 'features/Player/BasePlayer/Hook';
import { useProgramTimer } from 'features/Player/Hook/useProgramTimer';
import { useLogger } from 'services/logger';

import { useProgramEndEventParams } from './types';

export const useProgramEndEvent = (params: useProgramEndEventParams) => {
  const { timerType } = params;

  const { logger } = useLogger();
  const { stop } = usePlayerActions();
  const {
    features: { lagTime },
  } = usePlayerFeatures();
  const {
    program: { startTimeUtc, endTimeUtc },
  } = usePlayerProgram();

  const {
    timer: { currentTime },
  } = usePlayerTimer();

  const { programTotalDuration, getCurrentTime } = useProgramTimer({
    startTimeUtc,
    endTimeUtc,
  });

  const ended = useMemo(() => {
    switch (timerType) {
      case 'seconds':
        return (
          getCurrentTime(currentTime) >= programTotalDuration &&
          programTotalDuration > 0
        );
      // lagtime added for case when we play soe with lag and lead time
      case 'timestamp':
        return currentTime >= endTimeUtc + lagTime;
    }
  }, [
    currentTime,
    endTimeUtc,
    getCurrentTime,
    lagTime,
    programTotalDuration,
    timerType,
  ]);

  useEffect(() => {
    if (ended) {
      logger.debug('Program ended');

      stop();
    }
  }, [stop, logger, ended]);
};
