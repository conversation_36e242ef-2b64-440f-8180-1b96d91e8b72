import { useCallback, useMemo } from 'react';

import {
  isInitialPlayback,
  usePlayerFeatures,
  usePlayerPlayback,
} from 'features/Player/Context';
import { PlayerType } from 'features/Player/types';

import { useProgramTimerParams } from './types';

export const useProgramTimer = (params: useProgramTimerParams) => {
  const { startTimeUtc, endTimeUtc } = params;

  const { playback } = usePlayerPlayback();
  const {
    features: { leadTime, lagTime, catchup, startOver },
  } = usePlayerFeatures();

  const programBaseDuration = useMemo(
    () => endTimeUtc - startTimeUtc,
    [endTimeUtc, startTimeUtc],
  );

  const programTotalDuration = useMemo(() => {
    if (catchup || startOver) {
      return programBaseDuration + lagTime + leadTime;
    }
    return programBaseDuration;
  }, [programBaseDuration, catchup, lagTime, leadTime, startOver]);

  const startPosition = useMemo((): number => {
    if (isInitialPlayback(playback)) {
      return 0;
    }

    const startPositions = {
      [PlayerType.Startover]: startTimeUtc,
      [PlayerType.Catchup]: leadTime,
      [PlayerType.Channel]: Date.now(),
      [PlayerType.Recording]: 0,
      [PlayerType.Trailer]: 0,
      [PlayerType.Vod]: 0,
    };

    return startPositions[playback.type];
  }, [leadTime, playback, startTimeUtc]);

  const getCurrentTime = useCallback(
    (time: number): number => Math.max(time - leadTime, 0),
    [leadTime],
  );

  const createResumePositionForContinueWatching = useCallback(
    (currentTime: number): number => {
      if (isInitialPlayback(playback)) {
        return currentTime;
      }

      const getResumePositionForRecording = () => {
        // Lead time is not integral part of recording etc.
        // but all others platforms send resumePosition with
        // included leadTime, so we need to keep consistency
        // between ios, android and oldWeb
        const resumePosition = getCurrentTime(currentTime) + leadTime;
        return resumePosition > programBaseDuration
          ? programBaseDuration
          : resumePosition;
      };

      const getResumePositionForCatchup = () => {
        const resumePosition = getCurrentTime(currentTime);
        return resumePosition > programBaseDuration
          ? programBaseDuration
          : resumePosition;
      };

      const getResumePositionForLiveContent = () => {
        // catchup played in lagTime after manifest change from dynamic to static
        if (currentTime < programBaseDuration) {
          return currentTime + leadTime;
        }
        // startover in lagtime
        if (currentTime - startTimeUtc > programBaseDuration) {
          return 0;
        }
        return Math.max(currentTime - startTimeUtc, 0);
      };

      const resumePosition = {
        [PlayerType.Channel]: getResumePositionForLiveContent,
        [PlayerType.Startover]: getResumePositionForLiveContent,
        [PlayerType.Catchup]: getResumePositionForCatchup,
        [PlayerType.Recording]: getResumePositionForRecording,
        [PlayerType.Trailer]: () => currentTime,
        [PlayerType.Vod]: () => currentTime,
      };

      return resumePosition[playback.type]();
    },
    [playback, startTimeUtc, getCurrentTime, programBaseDuration, leadTime],
  );

  const getResumePosition = useCallback(
    (currentTime: number): number => {
      if (isInitialPlayback(playback)) {
        return 0;
      }

      const timestampedContentResumePosition = startTimeUtc + currentTime;
      const currentTimeWithLeadTime = currentTime + leadTime;
      const resumePosition = {
        [PlayerType.Startover]: timestampedContentResumePosition,
        [PlayerType.Channel]: timestampedContentResumePosition,
        [PlayerType.Recording]: currentTime,
        [PlayerType.Catchup]: currentTimeWithLeadTime,
        [PlayerType.Trailer]: 0,
        [PlayerType.Vod]: 0,
      };

      return resumePosition[playback.type];
    },
    [leadTime, playback, startTimeUtc],
  );

  return {
    startTimeUtc,
    startPosition,
    programBaseDuration,
    programTotalDuration,
    getCurrentTime,
    getContinueWatchingPosition: createResumePositionForContinueWatching,
    getResumePosition,
  };
};
