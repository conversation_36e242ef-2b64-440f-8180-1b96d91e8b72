import { useEffect, useRef } from 'react';

import { useLogger } from 'services/logger';

import { useSessionWatcherParams } from './types';

export const useSessionWatcher = (params: useSessionWatcherParams) => {
  const { onSessionClose, disable } = params;

  const mount = useRef<boolean | undefined>(undefined);

  const { logger } = useLogger();

  useEffect(() => {
    mount.current = true;

    return () => {
      mount.current = false;
    };
  }, []);

  useEffect(() => {
    return () => {
      // we use mount here to keep track if component is mounted or not, since
      // we need to pass closeSession as dependency with the player current time
      // and that dependency will make hook mount/unmount every player timer change
      if (mount.current === false && !disable) {
        logger.debug('Closing session');
        onSessionClose();
      }
    };
  }, [onSessionClose, logger, disable]);
};
