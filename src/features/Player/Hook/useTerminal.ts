import { useCallback, useMemo } from 'react';

import { usePlayerPlayback } from 'features/Player/Context';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { useGlobalLoaderContext } from 'services/loader/context';
import { useConfig } from 'services/config';
import { generateTerminalSerialNumber, getBrowserType } from 'services/device';
import { useUserSession } from 'services/user';
import { useTerminalUnregisterProcess } from 'features/TerminalUnregisterProcess/Context';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { useErrorScreen } from 'services/error/ErrorScreenContext';
import {
  isHouseholdSuspendError,
  isMaxSlotsExceededError,
} from 'services/error/guards';
import {
  useDeviceRegisterMutation,
  useRegisteredTerminalsQuery,
} from 'services/api/newApi/core/device/queries';

import { RegisterTerminalParams, useTerminalValue } from './types';

import { PlayerMode } from '../types';

export const useTerminal = (): useTerminalValue => {
  const { playback, reset: resetPlayback, setPlayback } = usePlayerPlayback();
  const { logout } = useUserSession();
  const { mutateAsync: addDeviceAsync } = useDeviceRegisterMutation();
  const { refetch: refetchDevices } = useRegisteredTerminalsQuery(false);

  const { setIsLoaderVisible } = useGlobalLoaderContext();
  const { showErrorModal } = useErrorScreen();
  const { startProcess: startTerminalUnregisterProcess } =
    useTerminalUnregisterProcess();
  const { setMode } = useAppLayoutMode();

  const {
    config: {
      terminal: {
        deviceType,
        deviceManufacture,
        deviceModel,
        serialNumberType,
      },
    },
  } = useConfig();

  const { data: householdInfo, isFetched: isHouseholdInfoFetched } =
    useCoreHouseholdQuery();

  const isUserInfoEmpty = useMemo(() => {
    return isHouseholdInfoFetched && !householdInfo;
  }, [isHouseholdInfoFetched, householdInfo]);

  const addNewDevice = useCallback(async () => {
    setIsLoaderVisible(true);
    if (!householdInfo) {
      return Promise.reject();
    }
    const { householdExtId } = householdInfo;
    const browserType = getBrowserType();
    await addDeviceAsync({
      name: browserType,
      serialNumber: generateTerminalSerialNumber(
        browserType,
        serialNumberType,
        householdExtId,
      ),
      deviceType,
      deviceManufacture,
      deviceModel,
    });
  }, [
    addDeviceAsync,
    deviceManufacture,
    deviceModel,
    deviceType,
    serialNumberType,
    setIsLoaderVisible,
    householdInfo,
  ]);

  const registerTerminal = useCallback(
    async ({ onSuccessTerminalDeleteCallback }: RegisterTerminalParams) => {
      if (isUserInfoEmpty) {
        return Promise.reject();
      }

      try {
        await addNewDevice();
        return Promise.resolve();
      } catch (error: unknown) {
        if (isHouseholdSuspendError(error)) {
          resetPlayback();
          logout();
          showErrorModal('HOUSEHOLD_SUSPENDED_ON_ACTION');
          return Promise.reject();
        }
        const restorePreviousPlayback = () => {
          setMode(PlayerMode.Expanded);
          setPlayback(playback);
        };
        if (isMaxSlotsExceededError(error)) {
          resetPlayback && resetPlayback();
          refetchDevices();
          startTerminalUnregisterProcess(
            onSuccessTerminalDeleteCallback
              ? onSuccessTerminalDeleteCallback
              : restorePreviousPlayback,
          );
        }
      } finally {
        setIsLoaderVisible(false);
      }
    },
    [
      setIsLoaderVisible,
      isUserInfoEmpty,
      addNewDevice,
      resetPlayback,
      refetchDevices,
      startTerminalUnregisterProcess,
      logout,
      showErrorModal,
      setMode,
      setPlayback,
      playback,
    ],
  );

  return { registerTerminal, addNewDevice };
};
