export const messages = {
  startover: {
    id: 'buttonPlayer.startover',
    defaultMessage: 'Od początku',
  },
  play: {
    id: 'buttonPlayer.play',
    defaultMessage: '<PERSON><PERSON><PERSON><PERSON><PERSON> (Spacja)',
  },
  pause: {
    id: 'buttonPlayer.pause',
    defaultMessage: '<PERSON><PERSON><PERSON><PERSON><PERSON> (Spacja)',
  },
  live: {
    id: 'buttonPlayer.live',
    defaultMessage: 'Na żywo',
  },
  rec: {
    id: 'buttonPlayer.rec',
    defaultMessage: 'Nagrywanie',
  },
  settings: {
    id: 'buttonPlayer.settings',
    defaultMessage: 'Ustawienia',
  },
  mini: {
    id: 'buttonPlayer.mini',
    defaultMessage: 'Okno',
  },
  fullscreen: {
    id: 'buttonPlayer.fullscreen',
    defaultMessage: 'Pełny ekran (f)',
  },
  expand: {
    id: 'buttonPlayer.expand',
    defaultMessage: 'Duży (f)',
  },
  mute: {
    id: 'buttonPlayer.mute',
    defaultMessage: 'Wycisz (m)',
  },
  unMute: {
    id: 'buttonPlayer.unMute',
    defaultMessage: 'Wył<PERSON><PERSON> wyciszenie (m)',
  },
  channels: {
    id: 'buttonPlayer.channels',
    defaultMessage: 'Kanały',
  },
  info: {
    id: 'buttonPlayer.info',
    defaultMessage: 'Informacje',
  },
};
