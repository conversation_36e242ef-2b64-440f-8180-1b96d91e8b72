import styled, { css } from 'styled-components';

import { IconButton } from 'components/Buttons/IconButton';
import { Text } from 'components/Typography';
import { blink } from 'theme/animations';

export const WrapperIcon = styled(IconButton)<{ mode?: string }>`
  position: relative;
  background-color: ${({ theme }) => theme.colors.white};
  border-radius: 50%;
  height: 5rem;
  width: 5rem;
  transition: 0.5s;

  &:hover {
    span {
      opacity: 1;
      transition: 0.3s;
    }

    background-color: ${({ theme }) => theme.colors.primary};
    svg {
      & > path {
        fill: ${({ theme }) => theme.colors.black};
      }
    }
  }

  ${({ mode }) =>
    mode === 'no-wrap' &&
    css`
      background-color: transparent;

      &:hover {
        background-color: transparent;
        svg {
          & > path {
            fill: ${({ theme }) => theme.colors.primary};
          }
        }
      }
    `}

  ${({ mode }) =>
    mode === 'expanded' &&
    css`
      background-color: ${({ theme }) => theme.colors.white};

      &:hover {
        background-color: ${({ theme }) => theme.colors.primary};
      }
    `}

  ${({ mode }) =>
    mode === 'background' &&
    css`
      background-color: ${({ theme }) => theme.colors.primary};

      &:hover {
        background-color: ${({ theme }) => theme.colors.primary};
        svg {
          & > path {
            fill: ${({ theme }) => theme.colors.white};
          }
        }
      }
    `}
`;

export const ClosePlayerButtonWrapper = styled(IconButton)`
  height: 5rem;
  width: 5rem;

  &:hover {
    svg {
      & > path {
        fill: ${({ theme }) => theme.colors.primary};
      }
      & > g > path {
        fill: ${({ theme }) => theme.colors.primary};
      }
    }
  }
`;

export const ExpandedButtonWrapper = styled(IconButton)`
  position: relative;

  &:hover > span {
    opacity: 1;
    transition: 0.3s;
  }
`;

export const TextStyled = styled(Text)`
  opacity: 0;
  position: absolute;
  top: 100%;
  padding-top: 1rem;
  text-align: center;
  white-space: nowrap;
  transform: translateX(-50%);

  color: ${({ theme }) => theme.colors.primary};
  font-size: ${({ theme }) => theme.fontSizes.xSmall};
`;

export const SettingsContainer = styled.div`
  position: relative;
`;

export const VolumeContainer = styled.div`
  position: relative;
`;

export const RecordingContainer = styled.div`
  position: relative;
`;

export const RecordingNowBlinkText = styled.div`
  position: absolute;
  right: 0;
  top: 0;
  width: 34px;
  height: 18px;
  transform: translate(-46%, 50%);
  background-color: ${({ theme }) => theme.colors.red};
  animation: ${blink} 1.6s steps(1) infinite;
  z-index: -1;
`;

const trackWidth = '1rem';
const thumbDiameter = '2.4rem';

export const VolumeInput = styled.input`
  transform: rotate(270deg);
  height: ${thumbDiameter};
  background-color: ${({ theme }) => theme.colors.transparent};
  position: relative;
  overflow: hidden;
  appearance: none;
  min-width: 160px;

  &::-webkit-slider-thumb {
    position: relative;
    appearance: none;
    width: ${thumbDiameter};
    height: ${thumbDiameter};
    border-radius: 50%;
    border: none;
    background: ${({ theme }) => theme.colors.primary};
    top: 50%;
    transform: translateY(-50%);
    box-shadow: calc(-100vmax - ${thumbDiameter}) 0 0 calc(100vmax)
      ${({ theme }) => theme.colors.primary};

    clip-path: polygon(
      100% 0,
      6.4px 0,
      0 calc((${thumbDiameter} - ${trackWidth}) * 0.5),
      -100vmax calc((${thumbDiameter} - ${trackWidth}) * 0.5),
      -100vmax calc(${thumbDiameter} - calc(
              (${thumbDiameter} - ${trackWidth}) * 0.5
            )),
      0.1rem
        calc(${thumbDiameter} - calc((${thumbDiameter} - ${trackWidth}) * 0.5)),
      0.1rem 100%,
      calc(100%) calc(100%)
    );
  }

  &::-webkit-slider-runnable-track {
    background-color: ${({ theme }) => theme.colors.dustyGray};
    height: ${trackWidth};
  }

  &::-moz-range-progress {
    background-color: ${({ theme }) => theme.colors.primary};
    height: ${trackWidth};
  }

  &::-moz-range-track {
    background-color: ${({ theme }) => theme.colors.dustyGray};
    height: ${trackWidth};
  }

  &::-moz-range-thumb {
    border: none;
    height: ${thumbDiameter};
    width: ${thumbDiameter};
    border-radius: 50%;
    background-color: ${({ theme }) => theme.colors.primary};
  }
`;

export const PictureInPictureIconWrapper = styled.div`
  width: 4rem;
  height: 4rem;
  background-color: ${({ theme }) => theme.colors.black40};
  border-radius: 0.4rem;
  border: 0.1rem solid ${({ theme }) => theme.colors.gray20};
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    filter: brightness(110%);
  }
`;
