import { PlayerMode } from 'features/Player/types';
import { Channel } from 'services/api/newApi/live/channels/types';
import { ProgramDetails } from 'services/api/newApi/core/tvguide';

export interface PlayerModeButtonProps {
  modes: [PlayerMode, PlayerMode];
  buttonIcon: [JSX.Element, JSX.Element];
  textExpanded?: {
    id: string;
    defaultMessage: string;
  };
  fullScreen?: boolean;
}

export type InfoButtonProps = {
  fileId: string;
  isSubscribedChannel?: boolean;
  iconColorHover?: string;
};

export interface RecButtonProps {
  program: ProgramDetails;
  channel: Channel;
}

export type ChannelsButtonProps = {
  onClick: () => void;
  isSliderOpen: boolean;
};
