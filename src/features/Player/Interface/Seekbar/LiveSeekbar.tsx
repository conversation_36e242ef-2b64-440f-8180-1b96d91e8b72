import { millisecondsToSeconds } from 'date-fns';
import {
  ChangeEvent,
  FC,
  MouseEvent,
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { useProgramTimer } from 'features/Player/Hook';
import {
  PlayerDownKeys,
  PlayerUpKeys,
  usePlayerInstance,
  usePlayerTimeLimit,
  usePlayerTimer,
} from 'features/Player/BasePlayer/Context';
import { usePlayerFeatures, usePlayerProgram } from 'features/Player/Context';
import { getTimeFromTimestamp } from 'utils/dateUtils';
import { isEmpty } from 'utils/misc/isEmpty';
import { useModalNpvr } from 'services/user/ModalNpvr';
import { TimeInSeconds } from 'services/api/common/types';
import { useUserProfile } from 'services/user';
import { usePlayerKeyActions } from 'features/Player/BasePlayer/Context/PlayerKeyActionsContext';

import * as S from './styles';
import { LiveSeekbarProps, ScrubbingMode, TimePreview } from './types';
import {
  useLiveTimeEdges,
  useScrubbingMode,
  useSeekbarTimeLabel,
  useThumbnails,
} from './hooks';
import {
  CHROME_THUMB_MOVE_BREAKPOINT,
  LIVE_SEEKBAR_DECREASE_VALUE,
  LIVE_SEEKBAR_INCREASE_VALUE,
  SEEKBAR_DECIMAL_PRECISION,
  SEEKBAR_MOVE_STEP,
} from './constants';
import { CurrentTimePreview } from './components/CurrentTimePreview';
import { ScrubbingTimePreview } from './components/ScrubbingTimePreview';
import {
  getThumbnailContainerLeftPosition,
  getThumbnailTimestamp,
} from './helpers';

export const LiveSeekbar: FC<PropsWithChildren<LiveSeekbarProps>> = () => {
  const { player } = usePlayerInstance();
  const { features } = usePlayerFeatures();
  const { startDate, endDate } = useLiveTimeEdges(features);
  const { timer } = usePlayerTimer();
  const { program } = usePlayerProgram();
  const { resetTimer } = usePlayerTimeLimit();
  const { showNpvrModal } = useModalNpvr();
  const { isNpvrService } = useUserProfile();
  const { setKeyDownAction, setKeyUpAction } = usePlayerKeyActions();
  const { scrubbingMode, scrubbingEnabled } = useScrubbingMode();

  const isEpgAvailable = useMemo(() => !isEmpty(program), [program]);

  const [value, setValue] = useState(player?.currentTime || 0);
  const { programBaseDuration } = useProgramTimer({
    startTimeUtc: startDate ?? 0,
    endTimeUtc: endDate ?? 0,
  });

  // dynamic (live), static (catchup) - there is switch from dynamic to static when we are watching SOE in lag time, static manifest makes that for few minutest liveSeekbar need to handle catchups
  const isStaticManifest = useMemo(() => timer.duration !== 0, [timer]);

  const { timePreview, hideTimeLabel, showTimeLabel } =
    useSeekbarTimeLabel(scrubbingEnabled);

  const currentProgressPercent = useMemo(() => {
    if (!isEpgAvailable) return 100;
    if (isStaticManifest) {
      return Number(
        ((100 * timer.currentTime) / timer.duration).toFixed(
          SEEKBAR_DECIMAL_PRECISION,
        ),
      );
    }
    return Number(
      ((100 * (value - (startDate ?? 0))) / programBaseDuration).toFixed(
        SEEKBAR_DECIMAL_PRECISION,
      ),
    );
  }, [
    isEpgAvailable,
    isStaticManifest,
    value,
    startDate,
    programBaseDuration,
    timer.currentTime,
    timer.duration,
  ]);

  const inputRef = useRef<HTMLInputElement | null>(null);
  const virtualValueRef = useRef<number>(currentProgressPercent);
  const [scrubbing, setScrubbing] = useState<boolean | undefined>(undefined);

  // Update input value from player timer when no scrubbing
  useEffect(() => {
    if (!scrubbing && inputRef.current) {
      setValue(timer.currentTime);
      inputRef.current.value = timer.currentTime.toString();
    }
  }, [timer, scrubbing]);

  // Update player time when scrubbing ended, this will seek video
  useEffect(() => {
    // ignore scrubbing === undefined state to no reset current time when component unmounts
    if (!scrubbing && scrubbing !== undefined && player) {
      player.currentTime = virtualValueRef.current;
    }
  }, [scrubbing, player]);

  const getProperMoveValue = useCallback(
    (moveValue: number) => {
      const currentTime = millisecondsToSeconds(Date.now());
      const blockModes: ScrubbingMode[] = [
        'blockForward',
        'blockBackward',
        'fullBlock',
      ];
      // always block scrubbing ahead of head
      let isBlocked = moveValue > currentTime;
      if (scrubbingMode === 'fullBlock') {
        isBlocked = true;
      }
      // when block forward active, do not allow scrubbing to any forward position
      if (scrubbingMode === 'blockForward') {
        isBlocked = moveValue >= timer.currentTime;
      }
      if (scrubbingMode === 'blockBackward') {
        isBlocked = moveValue <= timer.currentTime;
      }
      if (isBlocked && inputRef.current) {
        // keep current position in blocking mode or jump to live position when
        // trying scrubbing over head
        const jumpPosition = blockModes.includes(scrubbingMode)
          ? timer.currentTime
          : currentTime;

        return jumpPosition;
      }
      return moveValue;
    },
    [scrubbingMode, timer.currentTime],
  );
  // Save input value to virtual value when user change input position, this
  // will not change video current time until user finish scrubbing
  const onInput = (event: ChangeEvent<HTMLInputElement>) => {
    const updatedValue = Number(event.target.value);

    event.preventDefault();
    const moveValue = getProperMoveValue(updatedValue);

    // // lead time is visible on seekbar and user can jump to that time
    virtualValueRef.current = moveValue;
    event.target.value = moveValue.toString();
  };

  const onStartScrubbing = useCallback(() => {
    if (!isNpvrService && features.startOver) {
      const now = millisecondsToSeconds(Date.now());
      setValue(now);
      return showNpvrModal();
    }

    scrubbingEnabled && setScrubbing(true);
    setValue(timer.currentTime);
  }, [
    features.startOver,
    isNpvrService,
    scrubbingEnabled,
    showNpvrModal,
    timer.currentTime,
  ]);

  const onEndScrubbing = useCallback(
    () => scrubbingEnabled && setScrubbing(false),
    [scrubbingEnabled],
  );

  const onChange = (event: ChangeEvent<HTMLInputElement>) => {
    const inputValue = Number(event.target.value).toFixed(
      SEEKBAR_DECIMAL_PRECISION,
    );
    setValue(Number(inputValue));
    resetTimer();
  };

  const onKeyUp = useCallback(() => {
    onEndScrubbing();
    hideTimeLabel();
  }, [hideTimeLabel, onEndScrubbing]);

  const moveSeekbarOnKey = useCallback(
    (moveValue: number) => {
      onStartScrubbing();
      resetTimer();

      if (inputRef.current && virtualValueRef.current) {
        showTimeLabel(TimePreview.TIME_ONLY);
        const newValue = Number(
          (Number(inputRef.current.value) + moveValue).toFixed(),
        );
        const properNewValue = getProperMoveValue(newValue);
        setValue(properNewValue);
        inputRef.current.value = properNewValue.toString();
        virtualValueRef.current = properNewValue;
      }
    },
    [onStartScrubbing, resetTimer, showTimeLabel, getProperMoveValue],
  );

  const [mouseHoverProgressRatio, setMouseHoverProgressRatio] = useState(0);
  const [thumbnailPreviewLeftPosition, setThumbnailPreviewLeftPosition] =
    useState(0);
  const { currentThumbnailUrl, setNewThumbnailUrl } = useThumbnails(player);

  const onMouseMove = (event: MouseEvent<HTMLInputElement>) => {
    showTimeLabel(TimePreview.TIME_WITH_THUMBNAIL);
    const seekBarWidth = inputRef.current?.getBoundingClientRect().width || 0;
    const mouseOffsetX = event.nativeEvent.offsetX;
    const mouseHoverRatio = mouseOffsetX / seekBarWidth;
    const thumbnailTimestamp: TimeInSeconds = getThumbnailTimestamp(
      mouseHoverRatio,
      programBaseDuration,
      isStaticManifest,
      startDate,
    );
    const isScrubbingBlocked =
      scrubbingMode === 'blockForward' &&
      mouseHoverRatio > currentProgressPercent / 100;
    const isMouseBeyondLive =
      !isStaticManifest &&
      thumbnailTimestamp > millisecondsToSeconds(new Date().getTime());

    if (isScrubbingBlocked || isMouseBeyondLive) {
      showTimeLabel(TimePreview.TIME_ONLY);
      return;
    }

    showTimeLabel(TimePreview.TIME_WITH_THUMBNAIL);
    setThumbnailPreviewLeftPosition(
      getThumbnailContainerLeftPosition(mouseOffsetX, seekBarWidth),
    );
    setMouseHoverProgressRatio(mouseHoverRatio);
    setNewThumbnailUrl(thumbnailTimestamp);
  };

  useEffect(() => {
    setKeyDownAction(PlayerDownKeys.ArrowRight, () =>
      moveSeekbarOnKey(LIVE_SEEKBAR_INCREASE_VALUE),
    );
    setKeyDownAction(PlayerDownKeys.ArrowLeft, () =>
      moveSeekbarOnKey(LIVE_SEEKBAR_DECREASE_VALUE),
    );
    setKeyUpAction(PlayerUpKeys.ArrowRight, onKeyUp);
    setKeyUpAction(PlayerUpKeys.ArrowLeft, onKeyUp);
  }, [moveSeekbarOnKey, onKeyUp, setKeyDownAction, setKeyUpAction]);

  const TimeLabel = {
    [TimePreview.NONE]: null,
    [TimePreview.TIME_ONLY]: (
      <CurrentTimePreview
        progressPercent={currentProgressPercent}
        formattedTime={
          isStaticManifest
            ? getTimeFromTimestamp(startDate + value)
            : getTimeFromTimestamp(value)
        }
      />
    ),
    [TimePreview.TIME_WITH_THUMBNAIL]: (
      <ScrubbingTimePreview
        offsetLeft={thumbnailPreviewLeftPosition}
        formattedTime={getTimeFromTimestamp(
          startDate + mouseHoverProgressRatio * programBaseDuration,
        )}
        thumbnailUrl={currentThumbnailUrl}
      />
    ),
  };

  return (
    <S.SeekbarContainer data-testid='LiveSeekbar-SeekbarContainer'>
      {isEpgAvailable && (
        <S.SeekbarText>{getTimeFromTimestamp(startDate)}</S.SeekbarText>
      )}
      <S.SeekbarInputsContainer
        data-testid='LiveSeekbar-SeekbarInputsContainer'
        onMouseEnter={() => showTimeLabel(TimePreview.TIME_ONLY)}
        onMouseLeave={hideTimeLabel}
      >
        <S.SeekbarInput
          type={'range'}
          mode='liveSeekbar'
          disabled={true}
          min={startDate}
          max={endDate}
          value={millisecondsToSeconds(Date.now())}
        />
        <S.SeekbarInput
          ref={inputRef}
          type='range'
          mode='regularLiveSeekbar'
          defaultValue={isStaticManifest ? 0 : startDate}
          step={SEEKBAR_MOVE_STEP}
          min={isStaticManifest ? 0 : startDate}
          max={isStaticManifest ? timer.duration : endDate}
          onInput={onInput}
          onMouseUp={onEndScrubbing}
          onKeyUp={onEndScrubbing}
          onMouseDown={onStartScrubbing}
          disabled={!scrubbingEnabled}
          onChange={onChange}
          $isOnStart={value === startDate}
          $shouldAddShadowForChrome={
            currentProgressPercent > CHROME_THUMB_MOVE_BREAKPOINT
          }
          onMouseMove={onMouseMove}
        />
        {TimeLabel[timePreview]}
      </S.SeekbarInputsContainer>
      {isEpgAvailable && (
        <S.SeekbarText>{getTimeFromTimestamp(endDate)}</S.SeekbarText>
      )}
    </S.SeekbarContainer>
  );
};
