import { usePlayerProgram } from 'features/Player/Context';
import {
  ChannelsButton,
  InfoButton,
  LiveButton,
  MiniToExpandedSwitchButton,
  PlayPauseButton,
  SettingsButton,
  StartoverButton,
  ToggleFullScreenButton,
  VolumeButton,
} from 'features/Player/Interface/Button';
import { CatchupSeekbar } from 'features/Player/Interface/Seekbar';
import { useTheme } from 'theme';
import { useConfig } from 'services/config';
import { ChannelsSlider } from 'features/Player/Interface/ChannelsSlider';
import { createProgramName } from 'utils/misc/createProgramName';

import * as S from './styles';
import { useChannelsPlayerSlider } from './hooks';

import { ChannelLabel } from '../ChannelLabel/ChannelLabel';

export const CatchupFullScreenPlayerControls = () => {
  const theme = useTheme();
  const { program, channel } = usePlayerProgram();
  const { getTechConfig } = useConfig();
  const { withVisibleNpvrFeatures } = getTechConfig();
  const {
    isChannelsSliderVisible,
    toggleIsChannelsSliderVisible,
    containerRef,
  } = useChannelsPlayerSlider();

  return (
    <S.PlayerControlsContainer>
      <S.TopWrapper ref={containerRef}>
        {isChannelsSliderVisible ? (
          <ChannelsSlider currentChannelIndex={channel.channelExtId} />
        ) : (
          <ChannelLabel
            name={createProgramName(
              program?.name,
              program?.series?.episodeNumber,
            )}
            logo={channel.logoSignature}
          />
        )}
        <CatchupSeekbar />
      </S.TopWrapper>
      <S.PlayerControls>
        <S.LeftButtons>
          <S.InfoButtonWrapper>
            <InfoButton
              fileId={program.programExtId}
              isSubscribedChannel={channel.isSubscribed}
              iconColorHover={theme.colors.info}
            />
          </S.InfoButtonWrapper>
          <ChannelsButton
            onClick={toggleIsChannelsSliderVisible}
            isSliderOpen={isChannelsSliderVisible}
          />
        </S.LeftButtons>
        <S.CenterButtons>
          {withVisibleNpvrFeatures && <StartoverButton />}
          <PlayPauseButton />
          <LiveButton />
        </S.CenterButtons>
        <S.AsideButtons>
          <VolumeButton />
          <SettingsButton />
          <MiniToExpandedSwitchButton />
          <ToggleFullScreenButton />
        </S.AsideButtons>
      </S.PlayerControls>
    </S.PlayerControlsContainer>
  );
};
