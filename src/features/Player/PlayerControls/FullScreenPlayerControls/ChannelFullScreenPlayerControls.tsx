import { FC, PropsWithChildren } from 'react';

import { useTheme } from 'theme';
import {
  ChannelsButton,
  InfoButton,
  LiveButton,
  MiniToExpandedSwitchButton,
  PlayPauseButton,
  RecButton,
  SettingsButton,
  StartoverButton,
  ToggleFullScreenButton,
  VolumeButton,
} from 'features/Player/Interface/Button';
import { LiveSeekbar } from 'features/Player/Interface/Seekbar';
import { usePlayerProgram } from 'features/Player/Context';
import { isEmpty } from 'utils/misc/isEmpty';
import { useConfig } from 'services/config';
import { ChannelsSlider } from 'features/Player/Interface/ChannelsSlider';
import { createProgramName } from 'utils/misc/createProgramName';

import * as S from './styles';
import {
  ChannelPlayerControlsProps,
  ChannelPlayerControlsWithoutEpgProps,
} from './types';
import { useChannelsPlayerSlider } from './hooks';

import { ChannelLabel } from '../ChannelLabel';

const ChannelPlayerControls: FC<
  PropsWithChildren<ChannelPlayerControlsProps>
> = ({ program, channel }) => {
  const theme = useTheme();
  const { getTechConfig } = useConfig();
  const { withVisibleNpvrFeatures } = getTechConfig();
  const {
    isChannelsSliderVisible,
    toggleIsChannelsSliderVisible,
    containerRef,
  } = useChannelsPlayerSlider();

  return (
    <S.PlayerControlsContainer>
      <S.TopWrapper ref={containerRef}>
        {isChannelsSliderVisible ? (
          <ChannelsSlider currentChannelIndex={channel.channelExtId} />
        ) : (
          <ChannelLabel
            name={createProgramName(
              program.name,
              program?.series?.episodeNumber,
            )}
            logo={channel.logoSignature}
          />
        )}
        <LiveSeekbar />
      </S.TopWrapper>
      <S.PlayerControls>
        <S.LeftButtons>
          <S.InfoButtonWrapper>
            <InfoButton
              fileId={program.programExtId}
              isSubscribedChannel={channel.isSubscribed}
              iconColorHover={theme.colors.info}
            />
          </S.InfoButtonWrapper>
          <ChannelsButton
            onClick={toggleIsChannelsSliderVisible}
            isSliderOpen={isChannelsSliderVisible}
          />
        </S.LeftButtons>
        <S.CenterButtons $numberOfChildren={channel.isRecordingAllowed ? 4 : 3}>
          {withVisibleNpvrFeatures && <StartoverButton />}
          <PlayPauseButton />
          <LiveButton />
          {withVisibleNpvrFeatures && channel.isRecordingAllowed && (
            <RecButton program={program} channel={channel} />
          )}
        </S.CenterButtons>
        <S.AsideButtons>
          <VolumeButton />
          <SettingsButton />
          <MiniToExpandedSwitchButton />
          <ToggleFullScreenButton />
        </S.AsideButtons>
      </S.PlayerControls>
    </S.PlayerControlsContainer>
  );
};

const ChannelPlayerControlsWithoutEpg: FC<
  PropsWithChildren<ChannelPlayerControlsWithoutEpgProps>
> = ({ channel }) => {
  const {
    isChannelsSliderVisible,
    toggleIsChannelsSliderVisible,
    containerRef,
  } = useChannelsPlayerSlider();
  return (
    <S.PlayerControlsContainer>
      <S.TopWrapper ref={containerRef}>
        {isChannelsSliderVisible ? (
          <ChannelsSlider currentChannelIndex={channel.channelExtId} />
        ) : (
          <ChannelLabel name={''} logo={channel.logoSignature} />
        )}
        {}
      </S.TopWrapper>

      <LiveSeekbar />
      <S.PlayerControls>
        <S.LeftButtonsWithoutEpg>
          <ChannelsButton
            onClick={toggleIsChannelsSliderVisible}
            isSliderOpen={isChannelsSliderVisible}
          />
        </S.LeftButtonsWithoutEpg>
        <S.CenterButtons $numberOfChildren={1}>
          <PlayPauseButton />
        </S.CenterButtons>
        <S.AsideButtons>
          <VolumeButton />
          <SettingsButton />
          <MiniToExpandedSwitchButton />
          <ToggleFullScreenButton />
        </S.AsideButtons>
      </S.PlayerControls>
    </S.PlayerControlsContainer>
  );
};

export const ChannelFullScreenPlayerControls = () => {
  const { program, channel } = usePlayerProgram();
  if (isEmpty(program))
    return <ChannelPlayerControlsWithoutEpg channel={channel} />;

  return <ChannelPlayerControls program={program} channel={channel} />;
};
