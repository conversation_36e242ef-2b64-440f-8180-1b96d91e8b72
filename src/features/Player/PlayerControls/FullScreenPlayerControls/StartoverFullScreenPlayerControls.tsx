import { useTheme } from 'theme';
import {
  ChannelsButton,
  InfoButton,
  LiveButton,
  MiniToExpandedSwitchButton,
  PlayPauseButton,
  RecButton,
  SettingsButton,
  StartoverButton,
  ToggleFullScreenButton,
  VolumeButton,
} from 'features/Player/Interface/Button';
import { LiveSeekbar } from 'features/Player/Interface/Seekbar';
import { usePlayerProgram } from 'features/Player/Context';
import { useConfig } from 'services/config';
import { ChannelsSlider } from 'features/Player/Interface/ChannelsSlider';
import { createProgramName } from 'utils/misc/createProgramName';

import * as S from './styles';
import { useChannelsPlayerSlider } from './hooks';

import { ChannelLabel } from '../ChannelLabel/ChannelLabel';

export const StartoverFullScreenPlayerControls = () => {
  const theme = useTheme();
  const { program, channel } = usePlayerProgram();
  const { getTechConfig } = useConfig();
  const { withVisibleNpvrFeatures } = getTechConfig();
  const {
    isChannelsSliderVisible,
    toggleIsChannelsSliderVisible,
    containerRef,
  } = useChannelsPlayerSlider();

  return (
    <S.PlayerControlsContainer>
      <S.TopWrapper ref={containerRef}>
        {isChannelsSliderVisible ? (
          <ChannelsSlider currentChannelIndex={channel.channelExtId} />
        ) : (
          <ChannelLabel
            name={createProgramName(
              program.name,
              program?.series?.episodeNumber,
            )}
            logo={channel.logoSignature}
          />
        )}
        <LiveSeekbar isStartover />
      </S.TopWrapper>
      <S.PlayerControls>
        <S.LeftButtons>
          <S.InfoButtonWrapper>
            <InfoButton
              fileId={program.programExtId}
              isSubscribedChannel={channel.isSubscribed}
              iconColorHover={theme.colors.info}
            />
          </S.InfoButtonWrapper>
          <ChannelsButton
            onClick={toggleIsChannelsSliderVisible}
            isSliderOpen={isChannelsSliderVisible}
          />
        </S.LeftButtons>
        <S.CenterButtons $numberOfChildren={channel.isRecordingAllowed ? 4 : 3}>
          {withVisibleNpvrFeatures && <StartoverButton />}
          <PlayPauseButton />
          <LiveButton />
          {channel.isRecordingAllowed && (
            <RecButton program={program} channel={channel} />
          )}
        </S.CenterButtons>
        <S.AsideButtons>
          <VolumeButton />
          <SettingsButton />
          <MiniToExpandedSwitchButton />
          <ToggleFullScreenButton />
        </S.AsideButtons>
      </S.PlayerControls>
    </S.PlayerControlsContainer>
  );
};
