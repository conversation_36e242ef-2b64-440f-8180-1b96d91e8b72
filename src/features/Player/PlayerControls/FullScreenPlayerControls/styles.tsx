import styled from 'styled-components';

import { IconButton } from 'components/Buttons/IconButton';

export const PlayerControlsContainer = styled.div`
  position: absolute;
  bottom: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 5.6rem;
  padding-top: 5rem;
  background-image: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8) 90%);
`;

export const LabelsContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  column-gap: 1.6rem;
`;

export const TopWrapper = styled.div`
  width: 100%;
`;

export const PlayerControls = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
`;

export const PlayerControlsWithoutEpg = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
`;

export const AsideButtons = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: right;
  gap: 3.2rem;
  align-items: center;
  margin-right: 8.2rem;
`;

export const CenterButtons = styled.div<{ $numberOfChildren?: number }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 4.2rem;
  transform: ${({ $numberOfChildren }) => {
    switch ($numberOfChildren) {
      case 1:
        return '';
      case 2:
        return 'translateX(-4.6rem)';
      case 3:
        return '';
      case 4:
        return 'translateX(4.6rem)';
      default:
        return '';
    }
  }};
  align-self: center;
  justify-self: center;
`;

export const Spacer = styled.div``;

export const LeftButtons = styled.div`
  display: flex;
  align-items: left;
`;

export const LeftButtonsWithoutEpg = styled.div`
  display: flex;
  align-items: left;
  padding-left: 4.2rem;
`;

export const InfoButtonWrapper = styled.div`
  display: flex;
  align-items: left;
  margin-left: 10.6rem;
`;

export const PictureInPictureButtonWrapper = styled.div`
  position: absolute;
  bottom: 20rem;
  right: 1.8rem;
`;
