import { FC, PropsWithChildren } from 'react';

import { Epg as EpgComponent, Layout } from 'features/Program/planby';
import { useProgramContext } from 'routes/Program/context';
import { Loader } from 'components/Loader';

import { ChannelItem } from './ChannelItem';
import { ProgramItem } from './ProgramItem';
import * as S from './styles';
import { EpgProps } from './types';

export const Epg: FC<PropsWithChildren<EpgProps>> = () => {
  const {
    epgProps,
    layoutProps,
    isFetching,
    channelListFetching,
    isTransitioning,
  } = useProgramContext();

  return (
    <S.Container>
      <EpgComponent isLoading={isFetching || channelListFetching} {...epgProps}>
        <Layout
          {...layoutProps}
          renderProgram={({ program }) => (
            <ProgramItem key={program.data.id} program={program} />
          )}
          renderChannel={({ channel }) => (
            <ChannelItem key={channel.uuid} channel={channel} />
          )}
        />
      </EpgComponent>
      {isTransitioning && <Loader />}
    </S.Container>
  );
};
