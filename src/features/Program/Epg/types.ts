import { ParentControlLevel } from 'services/api/common/types';
import { PlayFeatures } from 'services/api/newApi/live/channels';

export interface Channel {
  logo: string;
  uuid: string;
  channelNumber: number;
  isSubscribed: boolean;
  isHomeZoneRestricted: boolean;
  playFeatures: PlayFeatures;
}

export interface ProgramI {
  channelUuid: string;
  description?: string;
  id: string;
  image: string;
  endDate: number;
  since: string;
  till: string;
  title: string;
  episodeNumber: string;
  prLevel: ParentControlLevel;
  isCatchupDisabled: boolean;
  isRecordingAllowed: boolean | undefined;
  isStartoverDisabled: boolean;
  isTveStreamDisabled: boolean;
  playFeatures: PlayFeatures | undefined;
  isSubscribedChannel: boolean | undefined;
  catchupDuration: number | undefined;
  isRegionalTv: boolean | undefined;
}

export interface EpgProps {}
