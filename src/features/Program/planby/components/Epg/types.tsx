import { Channel, Program } from 'features/Program/planby/helpers/types';
import { BaseTimeFormat, DateTime } from 'utils/dateUtils';

export interface EpgProps {
  width?: number;
  height?: number;
  isSidebar: boolean;
  isTimeline?: boolean;
  isLoading?: boolean;
  children?: React.ReactNode;
  loader?: React.ReactNode;
  sidebarWidth: number;
}

export interface useEpgProps {
  channels: Channel[];
  epg: Program[];
  width?: number;
  height?: number;
  startDate?: DateTime;
  selectedTime?: DateTime;
  endDate?: DateTime;
  isBaseTimeFormat?: BaseTimeFormat;
  isSidebar?: boolean;
  isTimeline?: boolean;
  $isLine?: boolean;
  dayWidth?: number;
  sidebarWidth?: number;
  itemHeight?: number;
  itemOverscan?: number;
  currentCategory?: string;
  setEpgBeginTime: (time: Date) => void;
  selectedDateTime: Date;
  minDate: Date;
  maxDate: Date;
  isFetching: boolean;
}
