import { useIntl } from 'react-intl';

import { PosterPreviewSlider } from 'components/PosterPreviewSlider';
import { PosterPreview } from 'components/PosterPreview';
import { getPublicAssetUrl } from 'utils/url';
import { DetailsViewType } from 'services/detailsView';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { VodPosterPreview } from 'components/VodPosterPreview';
import { SearchBox } from 'components/SearchBox';
import { Text } from 'components/Typography';
import { ChannelsGrid } from 'components/ChannelsGrid';

import * as S from './styles';
import { useSearch, useSearchOrder } from './hooks';
import { messages } from './messages';
import { SearchOrder } from './types';
import { areResultsNotEmpty } from './helpers';

export const Search = () => {
  const { formatMessage } = useIntl();
  const { setIsSearchOpen } = useAppLayoutMode();

  const {
    searchValue,
    setSearchValue,
    searchResults,
    isFetching,
    handleOpenDetails,
  } = useSearch(() => {
    setIsSearchOpen(false);
  });
  const { searchOrder } = useSearchOrder();

  const renderChannels = () =>
    searchResults &&
    areResultsNotEmpty(searchResults, 'channels') && (
      <S.SliderWrapper>
        <ChannelsGrid
          channels={searchResults.channels}
          isSlider={true}
          amountLines={1}
          title={messages.channelSlider.defaultMessage}
        />
      </S.SliderWrapper>
    );

  const renderLives = () =>
    searchResults &&
    areResultsNotEmpty(searchResults, 'lives') && (
      <S.SliderWrapper>
        <PosterPreviewSlider
          title={messages.liveSlider}
          isFetching={isFetching}
        >
          {searchResults.lives?.map(({ id, title, image, channelExtId }) => (
            <S.LivePosterContainer
              key={id}
              onClick={() =>
                handleOpenDetails(DetailsViewType.Program, id, channelExtId)
              }
              data-testid='Search-LivePosterContainer'
            >
              <PosterPreview title={title} src={getPublicAssetUrl(image)} />
            </S.LivePosterContainer>
          ))}
        </PosterPreviewSlider>
      </S.SliderWrapper>
    );

  const renderVods = () =>
    searchResults &&
    areResultsNotEmpty(searchResults, 'vods') && (
      <S.SliderWrapper>
        <PosterPreviewSlider title={messages.vodSlider} isFetching={isFetching}>
          {searchResults.vods?.map((vod) => (
            <div key={vod.id}>
              <S.VodPosterContainer
                onClick={() => {
                  handleOpenDetails(DetailsViewType.VOD, vod.id);
                }}
                data-testid='Search-VodPosterContainer'
              >
                <VodPosterPreview
                  title={vod.title}
                  src={getPublicAssetUrl(vod.image)}
                  id={vod.id}
                />
              </S.VodPosterContainer>
            </div>
          ))}
        </PosterPreviewSlider>
      </S.SliderWrapper>
    );

  const renderActors = () =>
    searchResults &&
    areResultsNotEmpty(searchResults, 'actors') && (
      <S.SliderWrapper>
        <PosterPreviewSlider
          title={messages.actorSlider}
          isFetching={isFetching}
        >
          {searchResults.actors?.map((actor, index) => (
            <div key={actor.id || `actor-${index}`}>
              <S.ActorPosterContainer onClick={() => {}}>
                <PosterPreview
                  src={getPublicAssetUrl(actor.image)}
                  placeholderType='actor'
                  title={actor.title}
                />
              </S.ActorPosterContainer>
            </div>
          ))}
        </PosterPreviewSlider>
      </S.SliderWrapper>
    );

  const renderResultsInProperOrder = {
    [SearchOrder.Default]: (
      <>
        {renderChannels()}
        {renderLives()}
        {renderVods()}
        {renderActors()}
      </>
    ),
    [SearchOrder.ProgramsOrder]: (
      <>
        {renderLives()}
        {renderChannels()}
        {renderVods()}
        {renderActors()}
      </>
    ),
    [SearchOrder.ChannelsOrder]: (
      <>
        {renderChannels()}
        {renderLives()}
        {renderVods()}
        {renderActors()}
      </>
    ),
    [SearchOrder.VodsOrder]: (
      <>
        {renderVods()}
        {renderLives()}
        {renderChannels()}
        {renderActors()}
      </>
    ),
  };

  return (
    <S.Container key='mainSearchContainer'>
      <SearchBox
        searchValue={searchValue}
        setSearchValue={setSearchValue}
        isOpen={true}
        toggleIsOpen={() => setIsSearchOpen(false)}
      />
      <S.ResultsContainer>
        {Boolean(searchResults) ? (
          renderResultsInProperOrder[searchOrder]
        ) : (
          <S.NoResultsContainer>
            <Text>{formatMessage(messages.noResults)}</Text>
          </S.NoResultsContainer>
        )}
      </S.ResultsContainer>
    </S.Container>
  );
};
