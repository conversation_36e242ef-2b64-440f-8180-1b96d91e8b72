import { customRenderWithLoader } from 'utils/testing';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { ReactNode } from 'react';
import { IntlProvider } from 'react-intl';

import { DiagnosticsCenter } from '../DiagnosticsCenter';
import { setupMockServer } from 'services/api/mock/mock.server';
import { getPmsTokenErrorWhenTerminalNotExistMockHandler } from 'services/api/newApi/core/device/mocks/pmsToken.mock';
import {
  addDeviceWithErrorMockHandlers,
  getDevicesMaxMockHandlers,
} from 'services/api/newApi/core/device/mocks/devicesHandlers.mock';

const mockUseDiagnosticsCenter = vi.fn();
const mockUsePmsReport = vi.fn();
const mockSetState = vi.fn();
const mockDiagnoseApp = vi.fn();
const mockSendDiagnosticReportToPms = vi.fn();

vi.mock('../hooks', () => ({
  useDiagnosticsCenter: () => mockUseDiagnosticsCenter(),
  usePmsReport: () => mockUsePmsReport(),
}));

vi.mock('components/Section', () => ({
  Section: ({ title, children }: { title: string; children: ReactNode }) => (
    <div>
      <h2>{title}</h2>
      <div>{children}</div>
    </div>
  ),
}));

vi.mock('components/Typography', () => ({
  Text: ({ children }: { children: ReactNode }) => <div>{children}</div>,
  H2: ({ children }: { children: ReactNode }) => <h2>{children}</h2>,
}));

vi.mock('components/Buttons/PrimaryButton', () => ({
  PrimaryButton: ({
    children,
    onClick,
    disabled,
  }: {
    children: ReactNode;
    onClick?: () => void;
    disabled?: boolean;
  }) => (
    <button onClick={onClick} disabled={disabled}>
      {children}
    </button>
  ),
}));

vi.mock('components/StatusIndicator', () => ({
  Status: {
    SUCCESS: 'success',
    ERROR: 'error',
    INACTIVE: 'inactive',
    LOADING: 'loading',
  },
  StatusIndicator: ({ status }: { status: string }) => (
    <div data-status={status}>Status: {status}</div>
  ),
}));

vi.mock('components/ModalConfirmation', () => ({
  ModalConfirmation: ({
    isOpen,
    onClose,
    modalTitle,
    modalDescription,
    buttonSubmitText,
    buttonDeniedText,
    onSubmit,
  }: {
    isOpen: boolean;
    onClose: () => void;
    modalTitle: string;
    modalDescription: string;
    buttonSubmitText: string;
    buttonDeniedText: string;
    onSubmit: () => void;
  }) =>
    isOpen && (
      <div>
        <h3>{modalTitle}</h3>
        <p>{modalDescription}</p>
        <button onClick={onSubmit}>{buttonSubmitText}</button>
        <button onClick={onClose}>{buttonDeniedText}</button>
      </div>
    ),
}));

vi.mock('components/Tooltip', () => ({
  Tooltip: ({ content }: { content: ReactNode }) => <div>{content}</div>,
}));

const mockRegisterTerminal = vi.fn();
const mockAddNewDevice = vi.fn();

vi.mock('features/Player/Hook', () => ({
  useTerminal: () => ({
    registerTerminal: mockRegisterTerminal,
    addNewDevice: mockAddNewDevice,
  }),
}));

vi.mock('services/config', () => ({
  useConfig: () => ({
    getTechConfig: () => ({
      diagnostics: {
        isHomeZoneConnectionCheckVisible: true,
      },
    }),
    config: {
      appConfig: {
        diagnosticsConfig: {
          configuration: {
            dnsResolveAddress: 'dns.example.com',
            homeZonePingAddress: 'homezone.example.com',
            edgeCachesCDNs: [
              { name: 'CDN1', url: 'cdn1.example.com' },
              { name: 'CDN2', url: 'cdn2.example.com' },
            ],
          },
        },
      },
    },
  }),
}));

vi.mock('utils/testing', () => ({
  customRenderWithLoader: (component: React.ReactNode) => {
    const { render } = require('@testing-library/react');
    return render(
      <IntlProvider locale='pl' messages={{}}>
        {component}
      </IntlProvider>,
    );
  },
}));

const defaultDiagnosticDetails = {
  osType: 'Windows',
  browserVersion: 'Chrome 90',
  timeZone: 'Europe/Warsaw',
  isOnline: true,
  householdExtId: '12345',
  appVersion: '1.0.0',
  dnsConnectionStatus: 'success',
  homeZoneConnectionStatus: 'success',
  connectionLocation: 'NEO',
  cdnsConnectionStatus: 'success',
  failedCdns: [],
};

describe('DiagnosticsCenter', () => {
  const server = setupMockServer();
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    server.resetHandlers();

    mockUseDiagnosticsCenter.mockReturnValue({
      state: 'NOT_DIAGNOSED',
      setState: mockSetState,
      diagnosticDetails: defaultDiagnosticDetails,
      diagnoseApp: mockDiagnoseApp,
    });

    mockUsePmsReport.mockReturnValue({
      sendDiagnosticReportToPms: mockSendDiagnosticReportToPms,
    });

    mockRegisterTerminal.mockImplementation(
      ({ onSuccessTerminalDeleteCallback }) => {
        onSuccessTerminalDeleteCallback();
      },
    );

    mockAddNewDevice.mockResolvedValue({});
  });

  it('should display enabled diagnose service button', async () => {
    render(<DiagnosticsCenter />);

    const button = await screen.findByRole('button', {
      name: /Diagnozuj usługę/i,
    });
    expect(button).toBeEnabled();
  });

  it('should display disabled diagnose service button after diagnostic', async () => {
    mockUseDiagnosticsCenter.mockReturnValue({
      state: 'DIAGNOSED',
      setState: mockSetState,
      diagnosticDetails: defaultDiagnosticDetails,
      diagnoseApp: mockDiagnoseApp,
    });

    render(<DiagnosticsCenter />);

    const button = await screen.findByRole('button', {
      name: /Diagnozuj usługę/i,
    });
    expect(button).toBeDisabled();
  });

  it('should display add new device modal and close on cancel', async () => {
    mockUseDiagnosticsCenter.mockReturnValue({
      state: 'ADD_TERMINAL',
      setState: mockSetState,
      diagnosticDetails: defaultDiagnosticDetails,
      diagnoseApp: mockDiagnoseApp,
    });

    render(<DiagnosticsCenter />);

    expect(screen.getByText('Brak urządzenia')).toBeInTheDocument();

    const cancelButton = screen.getByText('Anuluj');
    await user.click(cancelButton);

    expect(mockSetState).toHaveBeenCalled();
  });

  it('should display delete terminal modal on error', async () => {
    mockUseDiagnosticsCenter.mockReturnValue({
      state: 'ADD_TERMINAL',
      setState: mockSetState,
      diagnosticDetails: defaultDiagnosticDetails,
      diagnoseApp: mockDiagnoseApp,
    });

    server.use(
      ...getPmsTokenErrorWhenTerminalNotExistMockHandler,
      ...addDeviceWithErrorMockHandlers,
      ...getDevicesMaxMockHandlers,
    );

    render(<DiagnosticsCenter />);

    expect(screen.getByText('Brak urządzenia')).toBeInTheDocument();

    const submitButton = screen.getByText('Dodaj urządzenie');
    await user.click(submitButton);

    expect(mockRegisterTerminal).toHaveBeenCalled();
  });

  it('should call post report to pms after diagnostic', async () => {
    mockUseDiagnosticsCenter.mockReturnValue({
      state: 'DIAGNOSED',
      setState: mockSetState,
      diagnosticDetails: defaultDiagnosticDetails,
      diagnoseApp: mockDiagnoseApp,
    });

    render(<DiagnosticsCenter />);

    expect(mockSendDiagnosticReportToPms).toHaveBeenCalled();
  });
});

function render(component: React.ReactElement) {
  return customRenderWithLoader(component);
}
