import getResult from 'ua-parser-js';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { useGetPmsTokenMutation } from 'services/api/newApi/core/device';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { useConfig } from 'services/config';
import { postDiagnosticTestReport, Zone } from 'services/api/pmsApi';
import { usePmsReporter } from 'services/pmsReporter';
import { isNewServiceError, isOldServiceError } from 'services/error';
import { useLogger } from 'services/logger';
import { useToasts } from 'services/toasts';
import { Status } from 'components/StatusIndicator/types';

import { CDN_CONNECTION_ABORT_TIMEOUT, HOME_ZONE_VALUE } from './constants';
import {
  Cdn,
  DiagnosticCenterState,
  DiagnosticDetails,
  UsePmsReportParams,
} from './types';

import packageJson from '../../../../package.json';

export const useOnlineStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const goOnline = () => setIsOnline(true);
    const goOffline = () => setIsOnline(false);

    window.addEventListener('online', goOnline);
    window.addEventListener('offline', goOffline);

    return () => {
      window.removeEventListener('online', goOnline);
      window.removeEventListener('offline', goOffline);
    };
  }, []);

  return { isOnline };
};

export const useDiagnosticsCenter = () => {
  const deviceType = getResult();
  const { data: householdInfo } = useCoreHouseholdQuery();
  const [state, setState] = useState<DiagnosticCenterState>(
    DiagnosticCenterState.NOT_DIAGNOSED,
  );

  const [dnsConnectionStatus, setDnsConnectionStatus] = useState(
    Status.INACTIVE,
  );
  const [homeZoneConnectionStatus, setHomeZoneConnectionStatus] = useState(
    Status.INACTIVE,
  );
  const [connectionLocation, setConnectionLocation] = useState<
    Zone | undefined
  >(undefined);
  const [cdnsConnectionStatus, setCdnsConnectionStatus] = useState(
    Status.INACTIVE,
  );
  const [failedCdns, setFailedCdns] = useState<Array<Cdn>>([]);

  const osType = useMemo(() => {
    return deviceType.os.name;
  }, [deviceType.os.name]);

  const browserVersion = useMemo(() => {
    const { name, version } = deviceType.browser;
    return `${name} ${version}`;
  }, [deviceType.browser]);

  const {
    config: {
      appConfig: { diagnosticsConfig },
    },
  } = useConfig();

  const { isOnline } = useOnlineStatus();

  const checkCdnsConnection = async () => {
    if (!diagnosticsConfig) {
      return;
    }
    setCdnsConnectionStatus(Status.LOADING);
    const listOfFailedCdns: Array<Cdn> = [];

    const abortController = new AbortController();
    const signal = abortController.signal;

    setTimeout(() => abortController.abort(), CDN_CONNECTION_ABORT_TIMEOUT);
    const promises = diagnosticsConfig.configuration.edgeCachesCDNs.map((cdn) =>
      fetch(`https://${cdn.url}`, { signal })
        .then(() => ({ url: cdn.url, name: cdn.name, status: 'fulfilled' }))
        .catch(() => ({ url: cdn.url, name: cdn.name, status: 'rejected' })),
    );

    const resultsOfRequests = await Promise.allSettled(promises);

    resultsOfRequests.forEach((result) => {
      if (result.status === 'fulfilled' && result.value.status === 'rejected') {
        listOfFailedCdns.push({
          name: result.value.name,
          url: result.value.url,
        });
      }
    });

    setFailedCdns(listOfFailedCdns);
    setCdnsConnectionStatus(
      listOfFailedCdns.length === 0 ? Status.SUCCESS : Status.ERROR,
    );
  };

  const checkDns = async () => {
    if (!diagnosticsConfig) {
      return;
    }
    setDnsConnectionStatus(Status.LOADING);
    await fetch(`https://${diagnosticsConfig.configuration.dnsResolveAddress}`)
      .then(() => setDnsConnectionStatus(Status.SUCCESS))
      .catch(() => setDnsConnectionStatus(Status.ERROR));
  };

  const timeZone = useMemo(() => {
    const date = new Date();
    const timeZoneOffset = -date.getTimezoneOffset();
    const sign = timeZoneOffset >= 0 ? '+' : '-';
    const hours = String(Math.floor(Math.abs(timeZoneOffset) / 60)).padStart(
      2,
      '0',
    );
    const minutes = String(Math.abs(timeZoneOffset) % 60).padStart(2, '0');
    const gmtFormat = `GMT${sign}${hours}:${minutes}`;
    return `${Intl.DateTimeFormat().resolvedOptions().timeZone}, ${gmtFormat}`;
  }, []);

  const checkHomeZoneConnection = async () => {
    if (!diagnosticsConfig) {
      return;
    }

    const resultToZoneMap: Record<string, Zone> = {
      [Zone.NEO]: Zone.NEO,
      [Zone.PL]: Zone.PL,
    };

    setHomeZoneConnectionStatus(Status.LOADING);
    fetch(diagnosticsConfig.configuration.homeZonePingAddress)
      .then((response) => {
        response.text().then((result) => {
          setConnectionLocation(resultToZoneMap[result] || Zone.OTHER);

          if (result === HOME_ZONE_VALUE) {
            setHomeZoneConnectionStatus(Status.SUCCESS);
          } else {
            setHomeZoneConnectionStatus(Status.ERROR);
          }
        });
      })
      .catch(() => {
        setHomeZoneConnectionStatus(Status.ERROR);
      });
  };

  const diagnoseApp = async (isHomeZoneConnectionCheckVisible: boolean) => {
    try {
      isHomeZoneConnectionCheckVisible && (await checkHomeZoneConnection());
      await checkCdnsConnection();
      await checkDns();
    } finally {
      setState(DiagnosticCenterState.DIAGNOSED);
    }
  };

  const diagnosticDetails = useMemo(
    () => ({
      osType,
      browserVersion,
      timeZone,
      isOnline,
      householdExtId: householdInfo?.householdExtId,
      appVersion: packageJson.version,
      dnsConnectionStatus,
      homeZoneConnectionStatus,
      connectionLocation,
      cdnsConnectionStatus,
      failedCdns,
    }),
    [
      browserVersion,
      cdnsConnectionStatus,
      connectionLocation,
      dnsConnectionStatus,
      failedCdns,
      homeZoneConnectionStatus,
      isOnline,
      osType,
      timeZone,
      householdInfo?.householdExtId,
    ],
  );

  return {
    state,
    setState,
    diagnosticDetails,
    diagnoseApp,
  };
};

export const usePmsReport = ({ setState }: UsePmsReportParams) => {
  const { showToast } = useToasts();
  const { mutateAsync: getPmsToken } = useGetPmsTokenMutation();
  const { createBasePmsReport } = usePmsReporter();
  const { logger } = useLogger();
  const { data: householdInfo } = useCoreHouseholdQuery();

  const sendDiagnosticReportToPms = useCallback(
    async (diagnosticDetails: DiagnosticDetails) => {
      try {
        const pmsToken = await getPmsToken();
        if (!householdInfo) {
          throw new Error('User info not exist - send report canceled');
        }

        const report = createBasePmsReport(pmsToken, householdInfo);
        const {
          homeZoneConnectionStatus,
          dnsConnectionStatus,
          cdnsConnectionStatus,
          failedCdns,
          connectionLocation,
        } = diagnosticDetails;

        const extendedReport = {
          ...report,
          homezone: homeZoneConnectionStatus === Status.SUCCESS,
          zone: connectionLocation,
          dnsConnection: dnsConnectionStatus === Status.SUCCESS,
          cdnOplCheck: cdnsConnectionStatus === Status.SUCCESS,
          ...(cdnsConnectionStatus === Status.ERROR && {
            faultyEdgeCaches: failedCdns.map((failedCdn) => ({
              url: failedCdn.url,
            })),
          }),
        };

        await postDiagnosticTestReport(extendedReport);
        showToast('SEND_DIAGNOSTIC_TO_PMS_REPORT_SUCCESS');
        return setState(DiagnosticCenterState.DIAGNOSTIC_ENDED);
      } catch (error: unknown) {
        if (
          isNewServiceError(error) &&
          error.response.data.error === 'G000407'
        ) {
          return setState(DiagnosticCenterState.ADD_TERMINAL);
        }
        showToast('SEND_DIAGNOSTIC_TO_PMS_REPORT_ERROR');
        setState(DiagnosticCenterState.DIAGNOSTIC_ENDED);
        return logger.error(`Error while sending PMS report ${error}`);
      }
    },
    [
      createBasePmsReport,
      getPmsToken,
      logger,
      setState,
      showToast,
      householdInfo,
    ],
  );

  return {
    sendDiagnosticReportToPms,
  };
};
