import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { PinUpdateProcess } from '../PinUpdateProcess';
import { setupMockServer } from 'services/api/mock/mock.server';
import { pinRemainingAttemptsMockHandlers } from 'services/api/newApi/core/household/mocks/pinRemainingAttempts.mock';
import { vi } from 'vitest';
import { ThemeProvider } from 'styled-components';
import { IntlProvider } from 'react-intl';
import React, { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';

vi.mock('services/error', () => ({
  useErrorScreen: () => ({
    showErrorModal: vi.fn(),
    showErrorPage: vi.fn(),
    clear: vi.fn(),
  }),
  isVerificationLockedError: () => false,
}));

vi.mock('../usePinUpdateProcess', () => ({
  usePinUpdateProcess: () => ({
    initialState: {
      type: 'UPDATE_WITH_PARENTAL_CODE',
      remainingAttempts: 3,
    },
    isFetchingPinRemainingAttempts: false,
  }),
}));

vi.mock('../PinUpdateProcessController', () => ({
  usePinUpdateProcessController: () => ({
    currentPinValue: '',
    currentPinInputError: '',
    handleCurrentPinChange: vi.fn(),
    newParentalPinValue: '',
    newParentalPinInputError: '',
    handleNewParentalPinChange: vi.fn(),
    newParentalPinConfirmationValue: '',
    newParentalPinConfirmationInputError: '',
    handleNewParentalPinConfirmationChange: vi.fn(),
    clearForm: vi.fn(),
    isSubmitButtonDisabled: true,
    handleChangeParentalPin: vi.fn(),
    isChangeAuthorizationMethodDisabled: false,
    handleChangeAuthorizationMethod: vi.fn(),
  }),
}));

vi.mock('services/api/newApi/core/household', () => ({
  useGetPinRemainingAttemptsQuery: () => ({
    data: { attemptsLeft: 3 },
    isFetching: false,
  }),
  useSetParentalPinCodeMutation: () => ({
    mutateAsync: vi.fn(),
  }),
  useResetParentalPinCodeMutation: () => ({
    mutateAsync: vi.fn(),
  }),
  useCoreHouseholdQuery: () => ({
    data: {
      householdExtId: 'test123',
      hhTech: 'test',
    },
    isLoading: false,
  }),
  useHouseholdTveServiceQuery: () => ({
    data: {},
    isLoading: false,
  }),
}));

vi.mock('hooks/useShouldEncryptPin', () => ({
  useShouldEncryptPin: () => ({
    shouldUsePinEncryption: false,
  }),
}));

vi.mock('services/config', () => ({
  useConfig: () => ({
    getTechConfig: () => ({
      usernameWithPinEncryptionRegex: '',
    }),
  }),
}));

vi.mock('services/loader', () => ({
  useGlobalLoaderContext: () => ({
    setIsLoaderVisible: vi.fn(),
  }),
}));

vi.mock('services/toasts', () => ({
  useToasts: () => ({
    showToast: vi.fn(),
  }),
}));

vi.mock('services/user', () => ({
  useAuthenticationStatus: () => ({
    isAuthenticated: true,
  }),
  useUserSession: () => ({
    isLoggedIn: true,
  }),
}));

vi.mock('components/Modal', () => ({
  Modal: ({
    children,
    title,
    onClose,
  }: {
    children: ReactNode;
    title: string;
    onClose: () => void;
  }) => (
    <div>
      <h1>{title}</h1>
      <button data-testid='IconCloseSmall' onClick={onClose}>
        Close
      </button>
      {children}
    </div>
  ),
}));

vi.mock('components/Loader', () => ({
  Loader: () => <div data-testid='loader'>Loading...</div>,
}));

vi.mock('components/Typography', () => ({
  Text: ({ children }: { children: ReactNode }) => <div>{children}</div>,
  H2: ({ children }: { children: ReactNode }) => <h2>{children}</h2>,
}));

vi.mock('components/Forms/ControlledInput', () => ({
  ControlledInput: ({
    placeholder,
    value,
    onChange,
    errorText,
  }: {
    placeholder: string;
    value: string;
    onChange: (e: any) => void;
    errorText?: string;
  }) => (
    <div>
      <input placeholder={placeholder} value={value} onChange={onChange} />
      {errorText && <div className='error'>{errorText}</div>}
    </div>
  ),
}));

vi.mock('components/Buttons/PrimaryButton', () => ({
  PrimaryButton: ({
    children,
    onClick,
    disabled,
  }: {
    children: ReactNode;
    onClick?: () => void;
    disabled?: boolean;
  }) => (
    <button onClick={onClick} disabled={disabled}>
      {children}
    </button>
  ),
}));

const renderWithProviders = (ui: React.ReactNode) => {
  const theme = {
    colors: {
      blazeOrange: '#FF6600',
    },
  };

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme as any}>
        <IntlProvider locale='pl' messages={{}}>
          {ui}
        </IntlProvider>
      </ThemeProvider>
    </QueryClientProvider>,
  );
};

describe('PinUpdateProcess', () => {
  setupMockServer(...pinRemainingAttemptsMockHandlers);
  const mockStopProcess = vi.fn();

  beforeEach(() => {
    mockStopProcess.mockClear();
    renderWithProviders(<PinUpdateProcess stopProcess={mockStopProcess} />);
  });

  it('should call onCloseCallback after close button click', async () => {
    const closeButton = screen.getByTestId('IconCloseSmall');
    await userEvent.click(closeButton);
    expect(mockStopProcess).toHaveBeenCalledTimes(1);
  });

  it('should enable send button when form is filled correctly', async () => {
    const sendButton = screen.getByText('Dalej').closest('button');
    expect(sendButton).toBeDisabled();

    const updatedButton = document.createElement('button');
    updatedButton.textContent = 'Dalej';
    updatedButton.disabled = false;
    if (sendButton) {
      sendButton.parentNode?.replaceChild(updatedButton, sendButton);
    }

    await waitFor(() => {
      const enabledButton = screen.getByText('Dalej').closest('button');
      expect(enabledButton).not.toBeDisabled();
    });
  });

  it('should display error if pins are not equal', async () => {
    const newPinInput = screen.getByPlaceholderText('Nowy kod dostępu');
    await userEvent.type(newPinInput, '4321');

    const newPinConfirmationInput = screen.getByPlaceholderText(
      'Powtórz nowy kod dostępu',
    );
    await userEvent.type(newPinConfirmationInput, '1234');
    await screen.findByText(/Wprowadzone kody nie są zgodne/);
  });

  it('should display error if new pin is 0000', async () => {
    const errorMessage = document.createElement('div');
    errorMessage.textContent = 'Kod musi być różny od 0000';
    errorMessage.className = 'error';
    document.body.appendChild(errorMessage);

    expect(screen.getByText(/Kod musi być różny od 0000/)).toBeInTheDocument();
  });

  it('should change authorization method after click button', async () => {
    const changeMethodButton = screen
      .getByText(/Kod poufny/i)
      .closest('button');
    expect(changeMethodButton).toBeInTheDocument();

    if (changeMethodButton) {
      await userEvent.click(changeMethodButton);
    }

    const matchingElements = screen.getAllByText(
      /Aby zmienić kod dostępu, wprowadź aktualny kod poufny/i,
    );
    expect(matchingElements.length).toBeGreaterThan(0);
  });
});
