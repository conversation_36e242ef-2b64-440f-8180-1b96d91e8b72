import { useCallback, useState } from 'react';

import { useErrorScreen } from 'services/error';
import { useGlobalLoaderContext } from 'services/loader';
import {
  useDeviceUnregisterMutation,
  useRegisteredTerminalsQuery,
} from 'services/api/newApi/core/device/queries';
import { Terminal } from 'services/api/newApi/core/device/types';

import { UseTerminalUnregisterModalProps } from './types';

export const useTerminalUnregisterModal = (
  props: UseTerminalUnregisterModalProps,
) => {
  const { isOpen, callbackOnSuccessUnregister, stopProcess } = props;
  const { setIsLoaderVisible } = useGlobalLoaderContext();
  const { showErrorModal } = useErrorScreen();
  const { data: devices } = useRegisteredTerminalsQuery();
  const { mutateAsync: deleteDevice } = useDeviceUnregisterMutation();

  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Terminal>();

  const openConfirmationModal = useCallback(() => {
    setIsConfirmationModalOpen(true);
  }, []);

  const closeConfirmationModal = useCallback(() => {
    setIsConfirmationModalOpen(false);
  }, []);

  const handleDeleteTerminalClick = useCallback(
    (device: Terminal) => {
      setSelectedDevice(device);
      openConfirmationModal();
    },
    [openConfirmationModal],
  );

  const handleDeleteTerminalConfirmationClick = useCallback(async () => {
    stopProcess();
    if (selectedDevice) {
      setIsLoaderVisible(true);
      deleteDevice({ serialNumber: selectedDevice.serialNumber })
        .then(() => {
          callbackOnSuccessUnregister();
        })
        .catch(() => {
          showErrorModal('DELETE_FAIL');
        })
        .finally(() => {
          setIsLoaderVisible(false);
        });
    }
  }, [
    deleteDevice,
    callbackOnSuccessUnregister,
    selectedDevice,
    setIsLoaderVisible,
    showErrorModal,
    stopProcess,
  ]);

  return {
    devices,
    selectedDevice,
    handleDeleteTerminalClick,
    isConfirmationModalOpen,
    closeConfirmationModal,
    isOpen,
    handleDeleteTerminalConfirmationClick,
    stopProcess,
  };
};
