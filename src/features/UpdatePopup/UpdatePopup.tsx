import { FC, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';

import { ModalConfirmation } from 'components/ModalConfirmation';
import { routes } from 'routes/routes-map';

import { useUpdatePopup } from './hooks';
import { messages } from './messages';

import packageJson from '../../../package.json';

export const UpdatePopup: FC = () => {
  const { formatMessage } = useIntl();

  const { isUpdatePopupAlreadyShown, setIsUpdatePopupAlreadyShown } =
    useUpdatePopup();

  const [shouldNavigate, setShouldNavigate] = useState(false);
  const navigate = useNavigate();

  const handlePopupSubmit = () => {
    setIsUpdatePopupAlreadyShown(true);
    setShouldNavigate(true);
  };

  useEffect(() => {
    if (shouldNavigate) {
      navigate(routes.settings);
    }
  }, [shouldNavigate, navigate]);

  const handlePopupClose = () => {
    setIsUpdatePopupAlreadyShown(true);
  };

  return (
    <ModalConfirmation
      modalTitle={formatMessage(messages.popupTitle, {
        appVersion: packageJson.version,
      })}
      modalDescription={formatMessage(messages.popupText)}
      isOpen={!isUpdatePopupAlreadyShown}
      onClose={handlePopupClose}
      onSubmit={handlePopupSubmit}
      buttonSubmitText={formatMessage(messages.acceptButton)}
    />
  );
};
