import { FC, PropsWithChildren, useMemo } from 'react';
import { secondsToHours } from 'date-fns';
import { useIntl } from 'react-intl';

import { formatTimestampDate } from 'utils/dateUtils';
import { getPublicAssetUrl } from 'utils/url/asset';
import { useOrderVodVersionMutation } from 'services/api/oldApi/vod/queries';
import { useGlobalLoaderContext } from 'services/loader';
import { PaymentMode } from 'services/api/oldApi/vod/types';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { Text } from 'components/Typography';
import { Image } from 'components/Image';
import { CheckboxControl } from 'components/Forms/CheckboxControl';
import { Tooltip } from 'components/Tooltip';
import { getFormattedPrice } from 'utils/priceUtils';

import * as S from './styles';
import { PurchaseVODProps } from './types';
import { messages } from './messages';
import { PurchaseVODTerms } from './PurchaseVODTerms';
import { usePurchaseVOD } from './hooks';

import { useVodPurchaseDetails } from '../hooks/useVodPurchaseDetails';
import { PaymentMethod } from '../types';

export const PurchaseVOD: FC<PropsWithChildren<PurchaseVODProps>> = ({
  vodName,
  imageSource,
  purchaseInfo,
  dataDetailsView,
  onClickBackHandler,
}) => {
  const { versionExternalId } = purchaseInfo;

  const { formatMessage } = useIntl();
  const { areTermsAccepted, handleOnCheckTerms } = usePurchaseVOD();
  const { mutateAsync: purchaseVodVersionAsync } = useOrderVodVersionMutation({
    versionExternalId,
  });
  const { setIsLoaderVisible } = useGlobalLoaderContext();

  const {
    getOldestGiftId: getOldestGift,
    getPriceOfVodVersion,
    getVodPaymentMethod,
    numberOfGifts,
    isGiftAvailable,
    prepaidBalance,
    prepaidBalanceError,
  } = useVodPurchaseDetails(dataDetailsView);

  const vodPriceData = getPriceOfVodVersion(versionExternalId);

  const selectedPaymentMode = {
    [PaymentMethod.gift]: PaymentMode.internalPrepaid,
    [PaymentMethod.prepaid]: PaymentMode.internalPrepaid,
    [PaymentMethod.postpaid]: PaymentMode.normal,
  };

  const onClickPurchaseHandler = async () => {
    setIsLoaderVisible(true);
    await purchaseVodVersionAsync({
      discountId: purchaseInfo.discountId || '',
      giftId: isGiftAvailable ? getOldestGift()!.id : '',
      paymentMode: selectedPaymentMode[getVodPaymentMethod(versionExternalId)],
      price: vodPriceData.price,
      priceId: purchaseInfo.priceId,
    })
      .then(() => {
        onClickBackHandler();
      })
      .finally(() => {
        setIsLoaderVisible(false);
      });
  };

  const giftPayment = useMemo(
    () => (
      <>
        <S.DetailWrapper>
          <S.TextWithTooltip>
            <Text $sizeMedium>{formatMessage(messages.gift)}</Text>
            <Tooltip
              width={300}
              content={formatMessage(messages.giftInfoTooltip)}
            />
          </S.TextWithTooltip>
        </S.DetailWrapper>
        <S.DetailWrapper>
          <S.Label $secondary $sizeSmall>
            {formatMessage(messages.numberOfGiftsLabel)}
          </S.Label>
          <Text $sizeMedium>
            {numberOfGifts}
            &nbsp;
            {formatMessage(
              numberOfGifts === 1 ? messages.filmLabel : messages.filmsLabel,
            )}
          </Text>
        </S.DetailWrapper>
        <S.DetailWrapper>
          <S.Label $secondary $sizeSmall>
            {formatMessage(messages.giftActiveToLabel)}
          </S.Label>
          <Text $sizeMedium>
            {formatTimestampDate(getOldestGift()?.expirationDate || 0)}
          </Text>
        </S.DetailWrapper>
      </>
    ),
    [formatMessage, getOldestGift, numberOfGifts],
  );

  const prepaidPayment = useMemo(() => {
    if (prepaidBalanceError) {
      return null;
    }

    return (
      <>
        <S.DetailWrapper>
          <S.TextWithTooltip>
            <Text $sizeMedium>{formatMessage(messages.prepaid)}</Text>
            <Tooltip
              width={300}
              content={formatMessage(messages.prepaidInfoTooltip)}
            />
          </S.TextWithTooltip>
        </S.DetailWrapper>
        <S.DetailWrapper>
          <S.Label $secondary $sizeSmall>
            {formatMessage(messages.currentPrepaidBalanceLabel)}
          </S.Label>
          <Text $sizeMedium>
            {getFormattedPrice(prepaidBalance || '0')}
            {formatMessage(messages.priceUnit)}
          </Text>
        </S.DetailWrapper>
        <S.DetailWrapper>
          <S.Label $secondary $sizeSmall>
            {formatMessage(messages.prepaidBalanceAfterPurchaseLabel)}
          </S.Label>
          <Text $sizeMedium>
            {getFormattedPrice(
              (Number(prepaidBalance) - Number(vodPriceData.price)).toString(),
            )}
            {formatMessage(messages.priceUnit)}
          </Text>
        </S.DetailWrapper>
      </>
    );
  }, [formatMessage, prepaidBalance, vodPriceData.price, prepaidBalanceError]);

  const postpaidPayment = useMemo(
    () => (
      <S.DetailWrapper>
        <S.TextWithTooltip>
          <Text $sizeMedium>{formatMessage(messages.postpaid)}</Text>
          <Tooltip
            width={300}
            content={formatMessage(messages.postpaidInfoTooltip)}
          />
        </S.TextWithTooltip>
      </S.DetailWrapper>
    ),
    [formatMessage],
  );

  const selectedPaymentMethod = {
    [PaymentMethod.gift]: giftPayment,
    [PaymentMethod.prepaid]: prepaidPayment,
    [PaymentMethod.postpaid]: postpaidPayment,
  };

  return (
    <S.Container>
      <S.Wrapper>
        <S.VodPurchaseDetailsWrapper>
          <S.VodImageWrapper>
            <Image src={getPublicAssetUrl(imageSource)} alt={'name'} />
          </S.VodImageWrapper>
          <S.TextDetailsWrapper>
            <S.DetailWrapper>
              <S.Label $sizeSmall>
                {formatMessage(messages.purchaseNameLabel)}
              </S.Label>
              <Text $sizeMedium>{vodName}</Text>
            </S.DetailWrapper>
            <S.DetailWrapper>
              <S.Label $secondary $sizeSmall>
                {formatMessage(messages.purchasePriceLabel)}
              </S.Label>
              <Text $sizeMedium>
                {getFormattedPrice(
                  vodPriceData.giftWithDiscount ? '0' : vodPriceData.price,
                )}
                {formatMessage(messages.priceUnit)}
              </Text>
            </S.DetailWrapper>
            <S.DetailWrapper>
              <S.Label $secondary $sizeSmall>
                {formatMessage(messages.purchaseDurationLabel)}
              </S.Label>
              <Text $sizeMedium>
                {secondsToHours(purchaseInfo.rentalPeriod)}
                {formatMessage(messages.hoursUnit)}
              </Text>
            </S.DetailWrapper>
            <S.DetailWrapper>
              <S.Label $secondary $sizeSmall>
                {formatMessage(messages.purchasePaymentMethodLabel)}
              </S.Label>
              <Text $sizeMedium>
                {selectedPaymentMethod[getVodPaymentMethod(versionExternalId)]}
              </Text>
            </S.DetailWrapper>
          </S.TextDetailsWrapper>
        </S.VodPurchaseDetailsWrapper>
        <S.VodPurchaseRegulationsWrapper>
          <div>
            <Text $sizeMedium $primary>
              {formatMessage(messages.termsOfPurchase)}
            </Text>
          </div>
          <Text $sizeSmall> {formatMessage(messages.infoForCustomers)}</Text>
          <S.Terms>
            <PurchaseVODTerms />
          </S.Terms>
        </S.VodPurchaseRegulationsWrapper>
      </S.Wrapper>
      <S.BottomWrapper>
        <S.ButtonsWrapper>
          <PrimaryButton
            variant='default'
            type='button'
            onClick={onClickBackHandler}
          >
            <Text $primary>{formatMessage(messages.backButton)}</Text>
          </PrimaryButton>
          <PrimaryButton
            variant='orange'
            type='button'
            disabled={!areTermsAccepted}
            onClick={onClickPurchaseHandler}
          >
            <Text $primary>{formatMessage(messages.purchaseButton)}</Text>
          </PrimaryButton>
        </S.ButtonsWrapper>
        <S.CheckboxWrapper onClick={handleOnCheckTerms}>
          <CheckboxControl
            id='statistic-data-accepted'
            label={formatMessage(messages.acceptTerms)}
            isChecked={areTermsAccepted}
            onCheck={handleOnCheckTerms}
          />
        </S.CheckboxWrapper>
      </S.BottomWrapper>
    </S.Container>
  );
};
