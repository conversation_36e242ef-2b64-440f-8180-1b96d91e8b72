import { useCallback, useEffect } from 'react';

import { useUserPurchasesQuery, useVodAsset } from 'services/api/oldApi/vod';
import { ParentControlLevelNumber } from 'services/api/common/types';
import { getPublicAssetUrl } from 'utils/url';

import { DetailsVODProps } from '../types';
import {
  filterGenres,
  getCountriesName,
  getLanguagesName,
  getPictograms,
} from '../utils';
import { useVerifyVodAccess } from '../../VodAccess';

export const useDetailsVOD = ({
  dataDetailsView,
  setBackgroundImage,
}: Omit<DetailsVODProps, 'setData'>) => {
  const {
    data: vodData,
    isFetching,
    isError,
  } = useVodAsset({
    assetExternalId: dataDetailsView.id,
  });
  const { isVodAvailable } = useVerifyVodAccess({
    vod: vodData,
  });

  const { data: userPurchasesData } = useUserPurchasesQuery();

  const purchasedVODs = userPurchasesData?.assetPurchasedList?.find(
    (vod) => vod.vodAsset.assetExternalId === vodData?.assetExternalId,
  );
  const pictograms = vodData ? getPictograms(vodData.genres) : [];
  const genres = vodData ? filterGenres(vodData.genres, pictograms) : '';
  const dubbing = vodData ? getLanguagesName(vodData.dubbing) : '';
  const countries = vodData ? getCountriesName(vodData.countries) : '';

  const directors = vodData?.directors
    ?.map((director) => `${director.firstName} ${director.lastName}`)
    .join(', ');

  const actors = vodData?.actors?.map((actor) => ({
    name: `${actor.firstName} ${actor.lastName}`,
    img: actor.imagePath && getPublicAssetUrl(actor.imagePath),
  }));

  const handleSetBackgroundImage = useCallback(() => {
    const backgroundImage = getPublicAssetUrl(
      vodData?.vodCovers?.bannerImagePath || '',
    );
    setBackgroundImage(backgroundImage);
  }, [setBackgroundImage, vodData?.vodCovers?.bannerImagePath]);

  useEffect(() => {
    handleSetBackgroundImage();
  }, [handleSetBackgroundImage]);

  return {
    idVODDetails: dataDetailsView.id,
    title: vodData?.title,
    countries,
    year: vodData?.year,
    genres,
    directors,
    dubbing,
    purchasedVODs,
    desc: vodData?.description,
    actors,
    prLevel: vodData?.prLevel
      ? (parseInt(vodData.prLevel) as ParentControlLevelNumber)
      : undefined,
    isFetching: isFetching,
    isTrailerAvailable: vodData?.isTrailerAvailable,
    isError,
    pictograms,
    isVodAvailable,
    versions: vodData?.versions,
    vodImageSrc: vodData?.vodCovers?.verticalImagePath,
    handleSetBackgroundImage,
  };
};
