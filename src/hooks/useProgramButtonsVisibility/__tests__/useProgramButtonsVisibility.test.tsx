import { vi } from 'vitest';
import { useProgramButtonsVisibility } from '../useProgramButtonsVisibility';
import { renderHook } from 'utils/testing';

const programStartDate = new Date('June 10, 2023 13:00:00');
const programEndDate = new Date('June 10, 2023 15:00:00');

const catchupStartDate = new Date('June 07, 2023 13:00:00');
const catchupEndDate = new Date('June 07, 2023 15:00:00');

const programButtonsVisibilityBasicProps = {
  programId: 'lid721888168729503',
  isSubscribedChannel: true,
  startDate: programStartDate.getTime() / 1000,
  endDate: programEndDate.getTime() / 1000,
  playFeatures: {
    otg: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: true,
      isFastForwardBlocked: false,
    },
    conTv: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: true,
      isFastForwardBlocked: false,
    },
    stb: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: true,
      isFastForwardBlocked: false,
    },
    boxless: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: true,
      isFastForwardBlocked: false,
    },
  },
  catchupDuration: 604800,
  isCatchupDisabled: false,
  isStartoverDisabled: false,
  isRecordingAllowed: true,
};

const catchupProgramProps = {
  ...programButtonsVisibilityBasicProps,
  startDate: catchupStartDate.getTime() / 1000,
  endDate: catchupEndDate.getTime() / 1000,
};

describe('hooks:useProgramButtonsVisibility', () => {
  beforeEach(() => {
    vi.useFakeTimers().setSystemTime(
      new Date('June 10, 2023 13:30:00').getTime(),
    );
  });

  it('Should return proper values for LiveNow program', () => {
    const { result } = renderHook(() =>
      useProgramButtonsVisibility(programButtonsVisibilityBasicProps),
    );
    const expectedState = {
      shouldShowPlayButton: true,
      shouldShowRecordingButton: true,
      shouldShowStartoverButton: true,
      isRecordingNow: false,
      shouldShowCatchupButton: false,
      isLiveNow: true,
    };
    expect(result.current).toEqual(expectedState);
  });

  it('Should return proper values for Catchup program', () => {
    const { result } = renderHook(() =>
      useProgramButtonsVisibility(catchupProgramProps),
    );
    const expectedCatchupState = {
      shouldShowPlayButton: true,
      shouldShowRecordingButton: false,
      shouldShowStartoverButton: false,
      isRecordingNow: false,
      shouldShowCatchupButton: true,
      isLiveNow: false,
    };
    expect(result.current).toEqual(expectedCatchupState);
  });
});
