import { millisecondsToSeconds, secondsToMilliseconds } from 'date-fns';
import { useMemo } from 'react';

import { getLiveStatus, isFutureTime } from 'utils/dateUtils';
import { useConfig } from 'services/config';
import { TimeInSeconds } from 'services/api/common/types';
import { useChannelRecording } from 'components/Channel/hooks';
import {
  checkIfIsCatchupAvailable,
  checkIfIsProgramExpired,
} from 'utils/program';

import { UseProgramButtonsVisibilityProps } from './types';

export const checkIfIsNowOrFutureProgram = (endDate: TimeInSeconds) => {
  return isFutureTime(secondsToMilliseconds(endDate));
};

export const useProgramButtonsVisibility = ({
  programId,
  isSubscribedChannel,
  startDate,
  endDate,
  playFeatures,
  catchupDuration,
  isCatchupDisabled,
  isStartoverDisabled,
  isRecordingAllowed,
}: UseProgramButtonsVisibilityProps) => {
  const { isRecordingNow } = useChannelRecording(programId);

  const { withVisibleNpvrFeatures } = useConfig().getTechConfig();
  const isCatchupAvailable = checkIfIsCatchupAvailable({
    endDate,
    catchupDuration,
    isCatchupDisabled,
    playFeatures,
    withVisibleNpvrFeatures,
  });

  const isNowOrFutureProgram = checkIfIsNowOrFutureProgram(endDate);

  const isLiveNow = useMemo(() => {
    return getLiveStatus(
      secondsToMilliseconds(startDate),
      secondsToMilliseconds(endDate),
    );
  }, [endDate, startDate]);
  const timeNow = millisecondsToSeconds(new Date().getTime());

  const isProgramExpired = checkIfIsProgramExpired(endDate, catchupDuration);

  const shouldShowPlayButton = useMemo(() => {
    if (isLiveNow) {
      return true;
    }
    if (!playFeatures?.otg.isCatchUp) {
      return false;
    }

    if (isProgramExpired) {
      return false;
    }
    if (startDate && !isCatchupDisabled) {
      return startDate < timeNow;
    }
    if (!playFeatures.otg.isCatchUp) {
      return false;
    }
    return false;
  }, [
    isCatchupDisabled,
    isLiveNow,
    isProgramExpired,
    playFeatures?.otg.isCatchUp,
    startDate,
    timeNow,
  ]);

  const shouldShowStartoverButton = useMemo(() => {
    if (!isSubscribedChannel) {
      return false;
    }
    if (!withVisibleNpvrFeatures) {
      return false;
    }
    if (!isLiveNow) {
      return false;
    }
    if (!Boolean(isStartoverDisabled) && playFeatures?.otg.isStartOver) {
      return true;
    }
    return false;
  }, [
    isLiveNow,
    isStartoverDisabled,
    isSubscribedChannel,
    playFeatures?.otg.isStartOver,
    withVisibleNpvrFeatures,
  ]);

  const shouldShowRecordingButton = useMemo(() => {
    return (
      withVisibleNpvrFeatures && isNowOrFutureProgram && isRecordingAllowed
    );
  }, [isNowOrFutureProgram, isRecordingAllowed, withVisibleNpvrFeatures]);

  return {
    shouldShowPlayButton,
    shouldShowRecordingButton,
    shouldShowStartoverButton,
    isRecordingNow,
    shouldShowCatchupButton: isCatchupAvailable,
    isLiveNow,
  };
};
