import { vi } from 'vitest';
import { useProgramIconsVisibility } from '../useProgramIconsVisibility';
import { renderHook } from 'utils/testing';

const programStartDate = new Date('June 10, 2023 13:00:00');
const programEndDate = new Date('June 10, 2023 15:00:00');

const catchupStartDate = new Date('June 07, 2023 13:00:00');
const catchupEndDate = new Date('June 07, 2023 15:00:00');

const programIconsVisibilityBasicProps = {
  programId: 'lid721888168729503',
  isSubscribedChannel: true,
  startDate: programStartDate.getTime() / 1000,
  endDate: programEndDate.getTime() / 1000,
  playFeatures: {
    otg: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: true,
      isFastForwardBlocked: false,
    },
    conTv: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: true,
      isFastForwardBlocked: false,
    },
    stb: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: true,
      isFastForwardBlocked: false,
    },
    boxless: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: true,
      isFastForwardBlocked: false,
    },
  },
  catchupDuration: 604800,
  isCatchupDisabled: false,
  isStartoverDisabled: false,
  isRecordingAllowed: true,
};

const catchupProgramProps = {
  ...programIconsVisibilityBasicProps,
  startDate: catchupStartDate.getTime() / 1000,
  endDate: catchupEndDate.getTime() / 1000,
};

describe('hooks:useProgramIconsVisibility', () => {
  beforeEach(() => {
    vi.useFakeTimers().setSystemTime(
      new Date('June 10, 2023 13:30:00').getTime(),
    );
  });

  it('Should return proper values for LiveNow program', () => {
    const { result } = renderHook(() =>
      useProgramIconsVisibility(programIconsVisibilityBasicProps),
    );
    const expectedState = {
      shouldShowRecordingIcon: true,
      shouldShowStartoverIcon: true,
      shouldShowRecordingNowIcon: false,
      shouldShowCatchupIcon: false,
      shouldShowPlayLiveIcon: true,
    };
    expect(result.current).toEqual(expectedState);
  });

  it('Should return proper values for Catchup program', () => {
    const { result } = renderHook(() =>
      useProgramIconsVisibility(catchupProgramProps),
    );
    const expectedCatchupState = {
      shouldShowRecordingIcon: false,
      shouldShowStartoverIcon: false,
      shouldShowRecordingNowIcon: false,
      shouldShowCatchupIcon: true,
      shouldShowPlayLiveIcon: false,
    };
    expect(result.current).toEqual(expectedCatchupState);
  });
});
