import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { useConfig } from 'services/config';

export const useShouldEncryptPin = () => {
  const { data: householdInfo } = useCoreHouseholdQuery();
  const { getTechConfig } = useConfig();
  const { usernameWithPinEncryptionRegex } = getTechConfig();
  const techWithPinEncryption = new RegExp(usernameWithPinEncryptionRegex);

  let shouldUsePinEncryption = false;
  if (householdInfo) {
    shouldUsePinEncryption = techWithPinEncryption.test(
      householdInfo.householdExtId,
    );
  }

  return { shouldUsePinEncryption };
};
