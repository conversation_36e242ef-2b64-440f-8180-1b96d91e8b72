import { useState } from 'react';

import { useLocalStorage } from 'services/storage';
import { useConfig } from 'services/config';

import { ATV_APP_POPUP_ALREADY_SHOWN_STORAGE } from './constants';

export const useShowAtvAppPromoPopup = () => {
  const { getTechConfig } = useConfig();

  const [isAtvAppPopupAlreadyShown, setIsAtvAppPopupAlreadyShown] =
    useLocalStorage<boolean>(ATV_APP_POPUP_ALREADY_SHOWN_STORAGE, false);

  const canBeVisibleToCurrentUser = Boolean(
    getTechConfig()?.infoScreen?.isVisibleToUser,
  );

  const isPopupOpen = canBeVisibleToCurrentUser && !isAtvAppPopupAlreadyShown;

  const handlePopupClose = () => {
    setIsAtvAppPopupAlreadyShown(true);
  };

  return { isPopupOpen, handlePopupClose };
};

export const useUserMsgPopup = () => {
  const { config } = useConfig();
  const [isPopupOpen, setIsPopupOpen] = useState(true);

  const defaultConfig = config?.appConfig?.defaultConfig;
  const shouldShowPopup = Boolean(defaultConfig?.showUserMsg);

  const handlePopupClose = () => {
    setIsPopupOpen(false);
  };

  return {
    isPopupOpen: shouldShowPopup && isPopupOpen,
    handlePopupClose,
    userMsg: defaultConfig?.userMsg || '',
  };
};
