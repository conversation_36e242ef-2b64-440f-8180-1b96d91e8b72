import { useState } from 'react';
import { useConfig } from 'services/config';

export const useUserMsgPopup = () => {
  const { getTechConfig } = useConfig();
  const [isPopupOpen, setIsPopupOpen] = useState(true);

  const userMessage = getTechConfig()?.userMessage;
  const shouldShowPopup = Boolean(userMessage?.showUserMsg && userMessage?.userMsg);
  
  const handlePopupClose = () => {
    setIsPopupOpen(false);
  };

  return {
    isPopupOpen: shouldShowPopup && isPopupOpen,
    handlePopupClose,
    userMsg: userMessage?.userMsg || '',
  };
};
