import { useState } from 'react';

import { useConfig } from 'services/config';

export const useUserMsgPopup = () => {
  const { getTechConfig } = useConfig();
  const [isPopupOpen, setIsPopupOpen] = useState(true);

  const userMessage = getTechConfig()?.userMessage;
  const shouldShowPopup = Boolean(userMessage?.showUserMsg);

  console.log('UserMsgPopup Debug:', {
    userMessage,
    shouldShowPopup,
    isPopupOpen,
    finalResult: shouldShowPopup && isPopupOpen,
  });

  const handlePopupClose = () => {
    setIsPopupOpen(false);
  };

  return {
    isPopupOpen: shouldShowPopup && isPopupOpen,
    handlePopupClose,
    userMsg: userMessage?.userMsg || '',
  };
};
