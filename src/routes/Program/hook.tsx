import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  addDays,
  endOfDay,
  fromUnixTime,
  millisecondsToSeconds,
  startOfDay,
  subDays,
} from 'date-fns';

import { useEpg } from 'features/Program/planby';
import { SORTING_OPTIONS } from 'services/api/oldApi/config';
import {
  Channel as LiveChannel,
  useChannelsAllQuery,
} from 'services/api/newApi/live/channels';
import { useUserProfile } from 'services/user';
import { useMyListQuery } from 'services/api/oldApi/myzone';
import { useChannels } from 'routes/Channels/hooks';
import { getPublicAssetUrl } from 'utils/url';
import { Channel, ProgramI } from 'features/Program/Epg/types';
import {
  getCurrentDayStartDate,
  roundToTheNearestHour,
  setDayStartTime,
  timeToNextDayMs,
} from 'utils/dateUtils';
import { useEpgTimeContext } from 'features/Program/EpgControls/context';
import { ONE_DAY_LENGTH_MS } from 'utils/dateUtils/constants';
import {
  ChannelSchedule,
  ScheduleProgram,
  useTvguideEpgQuery,
} from 'services/api/newApi/core/tvguide';
import { globalConfig } from 'services/config/config';

import { INTERVAL_BETWEEN_SELECTABLE_HOURS, MAX_FILTERS } from './constants';
const { CHANNELS_CATEGORIES } = SORTING_OPTIONS;

export const useDailyEpg = () => {
  const [today, setToday] = useState(
    millisecondsToSeconds(getCurrentDayStartDate().getTime()),
  );

  const [intervalTime, setIntervalTime] = useState(timeToNextDayMs);

  useEffect(() => {
    const interval = setInterval(() => {
      setToday(millisecondsToSeconds(getCurrentDayStartDate().getTime()));
      setIntervalTime(ONE_DAY_LENGTH_MS);
    }, intervalTime);
    return () => {
      clearInterval(interval);
    };
  }, [intervalTime]);

  const { data: epg, isFetching: isEpgFetching } = useTvguideEpgQuery({
    date: today,
  });

  return { epg, isEpgFetching };
};

export const useProgram = () => {
  const { subscribedChannels } = useChannels();
  const { selectedDateTime, setSelectedDateTime } = useEpgTimeContext();
  const [shouldScrollToNow, setShouldScrollToNow] = useState(false);

  const { minDays, maxDays } = globalConfig.epgDatePicker;

  const defaultCategory = useMemo(() => {
    if (subscribedChannels.length > 0) {
      return CHANNELS_CATEGORIES.MY_PACKAGE;
    }
    return CHANNELS_CATEGORIES.ALL_CHANNELS;
  }, [subscribedChannels]);

  const { userHhTech } = useUserProfile();

  const [filterChannelsList, setFilterChannelsList] = useState<string[]>([]);

  const [selectedHour, setSelectedHour] = useState(
    roundToTheNearestHour(INTERVAL_BETWEEN_SELECTABLE_HOURS, new Date()),
  );

  const [categoryList, setCategoryList] = useState<string[]>([]);

  const [currentCategory, setCurrentCategory] =
    useState<string>(defaultCategory);

  const [channels, setChannels] = useState<Channel[]>([]);
  const [epg, setEpg] = useState<ProgramI[]>([]);
  const { data: myList } = useMyListQuery();
  const {
    data: liveChannels = [],
    isFetching: channelListFetching,
    error: channelsError,
    refetch: refetchChannels,
  } = useChannelsAllQuery();

  const { data, isFetching } = useTvguideEpgQuery({
    date: Math.floor(+selectedDateTime / 1000),
  });

  const channelsData = useMemo(() => channels, [channels]);
  const epgData = useMemo(() => epg, [epg]);

  const setEpgBeginTime = (time: Date, setToDayStart: boolean = true) => {
    const newTime = time;
    const dateTimeSelect = setToDayStart ? setDayStartTime(newTime) : newTime;

    const minDate = startOfDay(subDays(new Date(), minDays));
    const maxDate = endOfDay(addDays(new Date(), maxDays));

    const normalizedNewTime = startOfDay(newTime);

    if (
      normalizedNewTime.getTime() < minDate.getTime() ||
      normalizedNewTime.getTime() > maxDate.getTime()
    ) {
      return;
    }

    setSelectedDateTime(dateTimeSelect);
    setSelectedHour(
      roundToTheNearestHour(
        INTERVAL_BETWEEN_SELECTABLE_HOURS,
        setToDayStart ? newTime : new Date(),
      ),
    );
  };

  const calculateMaxDate = useCallback(() => {
    if (epg.length > 0) {
      const maxProgramDate = epg.reduce((latestDate, program) => {
        const programEndDate = fromUnixTime(program.endDate);
        return programEndDate > latestDate ? programEndDate : latestDate;
      }, new Date(0));
      return endOfDay(maxProgramDate);
    }
    return endOfDay(addDays(new Date(), maxDays));
  }, [epg, maxDays]);

  const minDate = startOfDay(subDays(new Date(), minDays));
  const maxDate = calculateMaxDate();

  const {
    getEpgProps,
    getLayoutProps,
    onScrollToNow,
    onScrollRight,
    onScrollLeft,
    onLongPressLeft,
    onLongPressRight,
    stopLongPress,
    isTransitioning,
    setIsTransitioning,
  } = useEpg({
    channels: channelsData,
    epg: epgData,
    startDate: selectedDateTime.toString(),
    selectedTime: selectedHour.toString(),
    currentCategory,
    setEpgBeginTime,
    selectedDateTime,
    minDate,
    maxDate,
    isFetching,
  });
  const getPlayFeatures = useCallback(
    (channelId: string, channelsInfo: LiveChannel[]) => {
      return channelsInfo.find((channel) => channel.channelExtId === channelId)
        ?.playFeatures;
    },
    [],
  );
  const canRecording = useCallback(
    (channelId: string, channelsInfo: LiveChannel[]) => {
      return channelsInfo.find((channel) => channel.channelExtId === channelId)
        ?.isRecordingAllowed;
    },
    [],
  );

  const checkIsSubscribedChannel = useCallback(
    (channelId: string, channelsInfo: LiveChannel[]) => {
      return channelsInfo.find((channel) => channel.channelExtId === channelId)
        ?.isSubscribed;
    },
    [],
  );

  const getCatchupDuration = useCallback(
    (channelId: string, channelsInfo: LiveChannel[]) => {
      return channelsInfo.find((channel) => channel.channelExtId === channelId)
        ?.catchupDuration;
    },
    [],
  );

  const checkIsRegionalTv = useCallback(
    (channelId: string, channelsInfo: LiveChannel[]) => {
      return channelsInfo.find((channel) => channel.channelExtId === channelId)
        ?.isRegionalTv;
    },
    [],
  );

  const handleFetchData = useCallback(() => {
    if (data && liveChannels) {
      const imagePaths = data.imagePaths;

      const epgFetch = data.guide.map((channel: ChannelSchedule) => {
        const channelSchedule = channel.programs
          .filter((program: ScheduleProgram) => {
            const programEndTime = program.endTimeUtc * 1000;
            const programStartTime = program.startTimeUtc * 1000;
            const selectedDayStart = startOfDay(selectedDateTime).getTime();
            const selectedDayEnd = endOfDay(selectedDateTime).getTime();

            return (
              (programStartTime >= selectedDayStart &&
                programStartTime <= selectedDayEnd) ||
              (programEndTime >= selectedDayStart &&
                programEndTime <= selectedDayEnd) ||
              (programStartTime <= selectedDayStart &&
                programEndTime >= selectedDayEnd)
            );
          })
          .map((program: ScheduleProgram) => {
            const startDate = new Date(program.startTimeUtc * 1000);
            const endDate = new Date(program.endTimeUtc * 1000);

            return {
              channelUuid: channel.channelExtId,
              id: program.programExtId,
              image:
                program.image &&
                getPublicAssetUrl(imagePaths.thumb, program.image),
              startDate: program.startTimeUtc,
              endDate: program.endTimeUtc,
              since: startDate.toISOString(),
              till: endDate.toISOString(),
              prLevel: program.prLevel,
              title: program.name,
              episodeNumber: program.episodeNumber,
              isCatchupDisabled: program.properties?.catchUpDisabled ?? false,
              isRecordingAllowed:
                !program.properties?.recordingDisabled &&
                canRecording(channel.channelExtId, liveChannels),
              isStartoverDisabled:
                program.properties?.startOverDisabled ?? false,
              playFeatures: getPlayFeatures(channel.channelExtId, liveChannels),
              isTveStreamDisabled:
                program.properties?.tveStreamDisabled ?? false,
              isSubscribedChannel: checkIsSubscribedChannel(
                channel.channelExtId,
                liveChannels,
              ),
              catchupDuration: getCatchupDuration(
                channel.channelExtId,
                liveChannels,
              ),
              isRegionalTv: checkIsRegionalTv(
                channel.channelExtId,
                liveChannels,
              ),
            };
          });

        return channelSchedule;
      });

      setEpg(epgFetch.flat());
    }
  }, [
    data,
    liveChannels,
    selectedDateTime,
    canRecording,
    getCatchupDuration,
    getPlayFeatures,
    checkIsSubscribedChannel,
    checkIsRegionalTv,
  ]);

  const handleFetchChannels = useCallback(
    (category: string, filters: string[]) => {
      if (data && liveChannels) {
        let currentChannels = liveChannels;

        if (category !== CHANNELS_CATEGORIES.ALL_CHANNELS) {
          currentChannels = liveChannels.filter(
            (channel) => channel.category === category,
          );
        }

        if (category === CHANNELS_CATEGORIES.FAVORITES && myList) {
          const myListChannels = myList.channelList?.map(
            (channel) => channel.channelExternalId,
          );
          currentChannels = liveChannels.filter(
            (channel) => myListChannels?.includes(channel.channelExtId),
          );
        }

        if (category === CHANNELS_CATEGORIES.MY_PACKAGE) {
          currentChannels = liveChannels.filter(
            (channel) => channel.isSubscribed,
          );
        }

        if (filters.length > 0) {
          const filteredChannels = currentChannels.filter(
            ({ name: channelName }) =>
              filters.some((filter) => {
                const normalizedChannelName = channelName
                  .replace(/\s+/g, '') // Removed spaces for better filtering
                  .toUpperCase();
                const normalizedChannelFilter = filter
                  .replace(/\s+/g, '')
                  .toUpperCase();
                return normalizedChannelName.includes(normalizedChannelFilter);
              }),
          );
          currentChannels = filteredChannels;
        }
        setChannels(
          currentChannels.map((channel) => ({
            logo: channel.logoUrl,
            uuid: channel.channelExtId,
            channelNumber: channel.channelNumber,
            isSubscribed: channel.isSubscribed,
            isHomeZoneRestricted: channel.isHomeZoneRestricted,
            playFeatures: channel.playFeatures,
          })),
        );
      }
    },
    [data, liveChannels, myList],
  );

  const createCategoryList = useCallback(() => {
    const categoriesSelectedFromChannels = liveChannels.map(
      (channel) => channel.category,
    );

    const basicCategories = [CHANNELS_CATEGORIES.ALL_CHANNELS];

    basicCategories.push(CHANNELS_CATEGORIES.FAVORITES);
    subscribedChannels.length > 0 &&
      basicCategories.push(CHANNELS_CATEGORIES.MY_PACKAGE);

    const channelCategories = Array.from(
      new Set([...basicCategories, ...categoriesSelectedFromChannels]),
    );

    setCategoryList(channelCategories);
  }, [liveChannels, subscribedChannels]);

  const deleteSingleFilter = (index: number) => {
    const newArr = [...filterChannelsList];
    newArr.splice(index, 1);
    setFilterChannelsList(newArr);
  };

  const canAddMoreFilters = useMemo(() => {
    return filterChannelsList.length < MAX_FILTERS;
  }, [filterChannelsList.length]);

  const handleAddFilter = (filter: string) => {
    if (canAddMoreFilters) {
      setFilterChannelsList((previous) =>
        previous.includes(filter) ? previous : [...previous, filter],
      );
    }
  };

  const categoryChannelFilter = (category: string) =>
    setCurrentCategory(category);

  const handleOnScrollNow = () => {
    setIsTransitioning(true);
    setEpgBeginTime(getCurrentDayStartDate(), false);
    setShouldScrollToNow(true);
  };

  useEffect(() => {
    if (shouldScrollToNow && !isFetching) {
      onScrollToNow();
      setIsTransitioning(false);
      setShouldScrollToNow(false);
    }
  }, [shouldScrollToNow, isFetching, onScrollToNow, setIsTransitioning]);

  const resetSelectedDateTime = useCallback(() => {
    setSelectedDateTime(getCurrentDayStartDate());
  }, [setSelectedDateTime]);

  const resetCurrentCategory = useCallback(() => {
    setCurrentCategory(defaultCategory);
  }, [defaultCategory]);

  const resetOnInit = useCallback(() => {
    resetSelectedDateTime();
    resetCurrentCategory();
  }, [resetCurrentCategory, resetSelectedDateTime]);

  useEffect(() => {
    if (!channelsError) {
      handleFetchChannels(currentCategory, filterChannelsList);
      handleFetchData();
    }
  }, [
    handleFetchData,
    handleFetchChannels,
    currentCategory,
    filterChannelsList,
    channelsError,
  ]);

  useEffect(() => {
    if (liveChannels.length) createCategoryList();
  }, [liveChannels, createCategoryList]);

  useEffect(() => {
    resetOnInit();
  }, [resetOnInit]);

  return {
    handleAddFilter,
    filterChannelsList,
    deleteSingleFilter,
    setEpgBeginTime,
    selectedDateTime,
    channelListFetching,
    isFetching,
    epgProps: getEpgProps(),
    layoutProps: getLayoutProps(),
    handleOnScrollNow,
    onScrollRight,
    onScrollLeft,
    onLongPressLeft,
    onLongPressRight,
    stopLongPress,
    categoryList,
    categoryChannelFilter,
    currentCategory,
    channelsError,
    refetchChannels,
    canAddMoreFilters,
    selectedHour,
    setSelectedHour,
    maxDate,
    minDate,
    isTransitioning,
  };
};
