import { LayoutProps } from 'features/Program/planby/components/Layout';
import { EpgProps } from 'features/Program/planby/components/Epg';

export interface ProgramContextValue {
  handleAddFilter: (filter: string) => void;
  filterChannelsList: string[];
  deleteSingleFilter: (index: number) => void;
  setEpgBeginTime: (time: Date) => void;
  selectedDateTime: Date;
  channelListFetching: boolean;
  isFetching: boolean;
  epgProps: EpgProps;
  layoutProps: LayoutProps;
  handleOnScrollNow: () => void;
  onScrollRight: (value?: number) => void;
  onScrollLeft: (value?: number) => void;
  onLongPressLeft: (value?: number) => void;
  onLongPressRight: (value?: number) => void;
  stopLongPress: () => void;
  categoryList: string[];
  categoryChannelFilter: (category: string) => void;
  currentCategory: string;
  channelsError: unknown;
  refetchChannels: () => void;
  canAddMoreFilters: boolean;
  selectedHour: Date;
  setSelectedHour: (time: Date) => void;
  isTransitioning: boolean;
}
