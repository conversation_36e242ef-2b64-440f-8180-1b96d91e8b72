import { useIntl } from 'react-intl';

import { RecordingPreviewGrid } from 'features/Recording/RecordingPreviewGrid';
import { RecordingPreviewSlider } from 'features/Recording/RecordingPreviewSlider';
import { Text } from 'components/Typography';

import { useRecordingViewMode } from './useRecordingViewMode';
import { useRecording } from './useRecording';
import { useSelectedRecordingSeries } from './useSelectedRecordingSeries';
import { normalizeRecordings } from './utils';

import { messages } from '../messages';
import * as S from '../styles';
import { UseRecordingViewInterface } from '../types';

export const useRecordingView = (): UseRecordingViewInterface => {
  const intl = useIntl();

  const { myRecordings, plannedRecordings } = messages;

  const {
    recorded,
    scheduled,
    isFetchingRecorded,
    isFetchingScheduled,
    isRecordingInfoMsgReady,
    infoText,
  } = useRecording();

  const { selectedSeries, setSelectedSeriesParams, clearSelectedSeries } =
    useSelectedRecordingSeries();
  const isSelectedSeriesReady = Boolean(selectedSeries);

  const {
    viewMode,
    isRecordedRecordingGrid,
    setIsRecordedRecordingGrid,
    setIsScheduledRecordingGrid,
  } = useRecordingViewMode();

  const renderRecordingPreviewGridForSelectedSeries = () => {
    if (selectedSeries) {
      const { seriesName, recordings, imagePaths } = selectedSeries;
      return (
        <RecordingPreviewGrid
          title={seriesName}
          data={normalizeRecordings(
            recordings,
            imagePaths ? imagePaths.thumb : '',
          )}
          isFetching={false}
          handleBackwardButtonClick={clearSelectedSeries}
          setSelectedSeriesParams={setSelectedSeriesParams}
        />
      );
    }
    return <></>;
  };

  const renderRecordingPreviewGrid = () => {
    return (
      <>
        <RecordingPreviewGrid
          title={
            isRecordedRecordingGrid
              ? intl.formatMessage(myRecordings)
              : intl.formatMessage(plannedRecordings)
          }
          data={isRecordedRecordingGrid ? recorded : scheduled}
          isFetching={
            isRecordedRecordingGrid ? isFetchingRecorded : isFetchingScheduled
          }
          handleBackwardButtonClick={
            isRecordedRecordingGrid
              ? () => setIsRecordedRecordingGrid(false)
              : () => setIsScheduledRecordingGrid(false)
          }
          setSelectedSeriesParams={setSelectedSeriesParams}
          isVisible={!isSelectedSeriesReady}
        />
        {selectedSeries && renderRecordingPreviewGridForSelectedSeries()}
      </>
    );
  };

  const recordingPreviewSliderForRecorded = () => (
    <RecordingPreviewSlider
      title={intl.formatMessage(myRecordings)}
      data={recorded}
      isFetching={isFetchingRecorded}
      withButtons={true}
      loop={false}
      setGridView={setIsRecordedRecordingGrid}
      setSelectedSeriesParams={setSelectedSeriesParams}
      isVisible={!isSelectedSeriesReady}
    />
  );

  const recordingPreviewSliderForScheduled = () => (
    <RecordingPreviewSlider
      title={intl.formatMessage(plannedRecordings)}
      data={scheduled}
      isFetching={isFetchingScheduled}
      withButtons={true}
      loop={false}
      setGridView={setIsScheduledRecordingGrid}
      setSelectedSeriesParams={setSelectedSeriesParams}
      isScheduled
      isVisible={!isSelectedSeriesReady}
    />
  );

  const recordingInfo = () => (
    <S.InfoContainer>
      {infoText.split('\n').map((str, index) => (
        <Text key={index}>{str}</Text>
      ))}
    </S.InfoContainer>
  );

  const renderRecordingSliders = () => (
    <>
      {isRecordingInfoMsgReady && recordingInfo()}
      {recordingPreviewSliderForRecorded()}
      {recordingPreviewSliderForScheduled()}
      {isSelectedSeriesReady && renderRecordingPreviewGridForSelectedSeries()}
    </>
  );

  return {
    viewMode,
    renderRecordingSliders,
    renderRecordingPreviewGrid: renderRecordingPreviewGrid,
    renderRecordingPreviewGridForSelectedSeries:
      renderRecordingPreviewGridForSelectedSeries,
    isSelectedSeriesReady,
  };
};
