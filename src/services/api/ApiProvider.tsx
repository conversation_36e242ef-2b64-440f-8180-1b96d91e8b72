import { useMemo } from 'react';
import {
  MutationCache,
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';

import {
  getUserErrorType,
  isAxiosError,
  isHouseholdSuspendError,
  isReadOnlyMaintenanceError,
  isRequestHeaderError,
  isServerError,
  isUnauthorizedError,
  useErrorScreen,
} from 'services/error';
import { useLogger } from 'services/logger';
import { deleteRefreshToken } from 'services/user/RefreshTokenHandlers';
import { useAuthenticationStatus } from 'services/user';

import { ApiProviderProps } from './types';
import { STALE_TIME_IN_MILLISECONDS } from './constants';

export const ApiProvider = (props: ApiProviderProps) => {
  const { children } = props;

  const { logger } = useLogger();
  const { showErrorModal } = useErrorScreen();
  const { setAuthenticated } = useAuthenticationStatus();

  // Do not create new QueryClient when AuthenticationProvider changed, otherwise
  // after client login/logout we are loosing current queryCache and invalidateQuery
  // can not works properly
  const queryClient = useMemo(() => {
    return new QueryClient({
      queryCache: new QueryCache({
        onError: (error: unknown) => {
          const isLoginPage = window.location.pathname.includes('/login');

          if (isUnauthorizedError(error) && !isLoginPage) {
            logger.error('User session is not longer valid');
            setAuthenticated(false);
            deleteRefreshToken();
            showErrorModal('NO_AUTHORIZATION');
            return;
          }

          if (isReadOnlyMaintenanceError(error)) {
            logger.error('System is in maintenance read only mode');
            showErrorModal(getUserErrorType(error));
          }

          if (isHouseholdSuspendError(error)) {
            setAuthenticated(false);
            showErrorModal('HOUSEHOLD_SUSPENDED_ON_ACTION');
          }
        },
      }),
      mutationCache: new MutationCache({
        onError: (error: unknown, _unusedA, _unusedB, mutation) => {
          if (isHouseholdSuspendError(error)) {
            const selectedError = mutation.meta?.isLoginMutation
              ? 'HOUSEHOLD_SUSPENDED_ON_LOGIN'
              : 'HOUSEHOLD_SUSPENDED_ON_ACTION';
            setAuthenticated(false);
            showErrorModal(selectedError);
          }
        },
      }),

      defaultOptions: {
        queries: {
          refetchOnWindowFocus: false,
          refetchOnReconnect: false,
          notifyOnChangeProps: 'tracked',
          staleTime: STALE_TIME_IN_MILLISECONDS,
          retry: false,
          useErrorBoundary: (error) => {
            return (
              isAxiosError(error) &&
              !isReadOnlyMaintenanceError(error) &&
              (isServerError(error) ||
                isRequestHeaderError(error) ||
                !error.response?.status)
            );
          },
        },
      },
    });
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
};
