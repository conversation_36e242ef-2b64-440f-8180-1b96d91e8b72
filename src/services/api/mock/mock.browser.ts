import { rest, setupWorker } from 'msw';
declare global {
  interface Window {
    msw: any;
  }
}

const mockResponse = rest.get(
  'gpapi/v2/vod/purchases/user-purchases',
  (req, res, ctx) => {
    return res(ctx.status(500));
  },
);

// Tymczasowy mock dla testowania G000015
const downForMaintenanceMock = rest.get('*', (req, res, ctx) => {
  // Odkomentuj poniższą linię żeby przetestować
  // return res(ctx.status(503), ctx.json({
  //   error: "G000015",
  //   errCode: "DOWN_FOR_MAINTENANCE",
  //   errMsg: "System is down for maintenance"
  // }));
  return req.passthrough();
});

export const worker = setupWorker(mockResponse, downForMaintenanceMock);

window.msw = {
  worker,
  rest,
};
