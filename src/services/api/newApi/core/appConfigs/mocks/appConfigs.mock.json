{"appConfigList": [{"type": "player", "revision": 1, "content": {"maxVerticalResolution": 720, "maxVerticalResolutionVod": 576, "refreshLiveSessionTimeRate": 900, "videoLagTime": 300, "videoLeadTime": 120, "widevineDrmUrl": "https://tvgo.ppd.orange.pl/RTEFacade_RIGHTV/widevinedrm"}, "version": "e9ba2270489550ed6647f8302afb59a2"}, {"type": "bitrate", "revision": 1, "content": {"maxBitrate": 4900, "maxBitrateVod": 3072, "sensitivity": {"live": "none", "other": "low"}}, "version": "e9ba2270489550ed6647f8302afb59a2"}, {"type": "tech_configurations", "revision": 1, "content": {"hhtechs": [{"configuration": {"channels": {"shouldIndicateHomeZone": false}, "diagnostics": {"isHomeZoneConnectionCheckVisible": false}, "mainTabs": {"isNpvrTabVisible": false}, "myZone": {"withContinueWatching": false, "withFavoriteVods": false, "withRecommendations": false}, "userInfo": {"isPurchaseHistoryVisible": false, "isTveSubscriptionInfoVisible": false}, "vods": {"isFavouriteActionVisible": false, "isPurchaseAllowed": false}, "withVisibleNpvrFeatures": false}, "name": "of_mob"}, {"configuration": {"channels": {"assets": [{"channelUnavailableWording": {"content": "Ka<PERSON>ł nie jest dostępny w Twoim pakiecie", "contentType": "wordingVal"}}], "shouldIndicateHomeZone": false}, "diagnostics": {"isHomeZoneConnectionCheckVisible": false}, "mainTabs": {"isNpvrTabVisible": false, "isVodTabVisible": false}, "myZone": {"withContinueWatching": false, "withFavoriteVods": false, "withRecommendations": false}, "npvrActivationPopup": {"assets": [{"noNpvrDescription": {"content": "Usługa niedostępna w Twoim pakiecie.", "contentType": "wordingVal"}}, {"noNpvrTitle": {"content": "Usługa niedostępna", "contentType": "wordingVal"}}]}, "userInfo": {"isPurchaseHistoryVisible": false, "isTveSubscriptionInfoVisible": false}, "vods": {"assets": [{"purchaseDisabled": {"content": "Usługa niedostępna w Twoim pakiecie", "contentType": "wordingVal"}}], "isFavouriteActionVisible": false, "isPurchaseAllowed": false}, "withVisibleNpvrFeatures": false}, "name": "of_nju"}, {"configuration": {"channels": {"shouldIndicateHomeZone": false}, "diagnostics": {"isHomeZoneConnectionCheckVisible": false}, "infoScreen": {"id": "planszaInformacyjnaATV", "isVisibleToUser": true}, "mainTabs": {"isNpvrTabVisible": false}, "myZone": {"withContinueWatching": false, "withFavoriteVods": false, "withRecommendations": false}, "userInfo": {"isPurchaseHistoryVisible": false, "isTveSubscriptionInfoVisible": false}, "vods": {"isFavouriteActionVisible": false, "isPurchaseAllowed": false}, "withVisibleNpvrFeatures": false}, "name": "of_fbb"}, {"configuration": {"npvrActivationPopup": {"assets": [{"noNpvrDescription": {"content": "Usługa niedostępna w twoim pakiecie", "contentType": "wordingVal"}}, {"noNpvrTitle": {"content": "Usługa niedostępna", "contentType": "wordingVal"}}]}}, "name": "dth"}, {"configuration": {"channels": {"assets": [{"channelUnavailableWording": {"content": "Chcesz mieć dostęp do tego kanału? Wejdź na www.orange.pl/pakiety i sprawdź ofertę z telewizją.", "contentType": "wordingVal"}}], "shouldIndicateHomeZone": true}, "diagnostics": {"isHomeZoneConnectionCheckVisible": true}, "infoScreen": {"id": "planszaInformacyjnaATV", "isVisibleToUser": false}, "mainTabs": {"isNpvrTabVisible": true, "isVodTabVisible": true}, "myZone": {"withContinueWatching": true, "withFavoriteChannels": true, "withFavoriteVods": true, "withPromoContent": true, "withRecentlyWatchedChannels": true, "withRecommendations": true, "withReminders": true}, "npvrActivationPopup": {"assets": [{"noNpvrDescription": {"content": "Aby <PERSON> z wybranej funkcjonalności aktywuj Multinagrywarkę na swoim dekoderze.", "contentType": "wordingVal"}}, {"noNpvrTitle": {"content": "Aktywuj <PERSON>gry<PERSON>ę", "contentType": "wordingVal"}}]}, "useOptionalRegionParam": false, "userInfo": {"isPurchaseHistoryVisible": true, "isTveSubscriptionInfoVisible": true}, "usernameWithPinEncryptionRegex": "^[MNF]", "vods": {"assets": [{"purchaseDisabled": {"content": "<PERSON><PERSON> z VOD musisz mieć ofertę z telewizją i dekoderem. Sprawdź na www.orange.pl/pakiety.", "contentType": "wordingVal"}}], "isFavouriteActionVisible": true, "isPurchaseAllowed": true}, "withVisibleNpvrFeatures": true, "userMessage": {"showUserMsg": true, "userMsg": "wia<PERSON><PERSON><PERSON><PERSON>\njakaś tam wiadomo<PERSON>."}}, "name": "default"}]}, "version": "e9ba2270489550ed6647f8302afb59a2"}, {"type": "rating", "revision": 1, "content": {"randomMaxTimeInMinutes": 2, "randomMinTimeInMinutes": 1, "showToast": true}, "version": "e9ba2270489550ed6647f8302afb59a2"}, {"type": "diagnostics", "revision": 1, "content": {"configuration": {"dnsResolveAddress": "tvgo.ppd.orange.pl", "edgeCachesCDNs": [{"name": "CDN00", "url": "redir.cache.orange.pl"}, {"name": "CDN01", "url": "ec01-krk3.cache.orange.pl"}, {"name": "CDN02", "url": "ec01-poz2.cache.orange.pl"}, {"name": "CDN03", "url": "ec01-ktw1.cache.orange.pl"}, {"name": "CDN05", "url": "ec01-poz3.cache.orange.pl"}, {"name": "CDN07", "url": "ec01-wmi1.cache.orange.pl"}, {"name": "CDN08", "url": "ec01-lcj2.cache.orange.pl"}, {"name": "CDN09", "url": "ec01-rdo1.cache.orange.pl"}, {"name": "CDN10", "url": "ec01-wro2.cache.orange.pl"}, {"name": "CDN11", "url": "ec01-szz2.cache.orange.pl"}, {"name": "CDN12", "url": "ec01-gdn2.cache.orange.pl"}, {"name": "CDN13", "url": "ec01-szy1.cache.orange.pl"}, {"name": "CDN14", "url": "ec01-bzg1.cache.orange.pl"}, {"name": "CDN15", "url": "ec01-luz2.cache.orange.pl"}, {"name": "CDN16", "url": "ec01-rze1.cache.orange.pl"}, {"name": "CDN17", "url": "ec01-krk2.cache.orange.pl"}, {"name": "CDN21", "url": "ec01-waw2.cache.orange.pl"}, {"name": "CDN22", "url": "ec01-waw4.cache.orange.pl"}, {"name": "CDN23", "url": "ec01-waw5.cache.orange.pl"}], "homeZonePingAddress": "https://tvgo.orange.pl/checkip"}}, "version": "e9ba2270489550ed6647f8302afb59a2"}]}