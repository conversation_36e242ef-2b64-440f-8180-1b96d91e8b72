export interface AppConfigsParams {
  group: string;
  types: Array<ConfigType>;
  revisions: Array<number>;
}

export enum ConfigType {
  PLAYER = 'player',
  BITRATE = 'bitrate',
  TECH_CONFIGURATIONS = 'tech_configurations',
  RATING = 'rating',
  DIAGNOSTICS = 'diagnostics',
  DEFAULT = 'default',
}

export interface AppConfigsResponse {
  appConfigList: Array<AppConfig>;
}

export interface AppConfig {
  content: ConfigContent;
  version: string;
  type: ConfigType;
  revision: number;
}

export type ConfigContent =
  | PlayerConfigContent
  | BitrateConfigContent
  | TechConfigContent
  | RatingConfigContent
  | DiagnosticsConfigContent
  | DefaultConfigContent;

export interface PlayerConfigContent {
  maxVerticalResolution: number;
  maxVerticalResolutionVod: number;
  refreshLiveSessionTimeRate: number;
  videoLagTime: number;
  videoLeadTime: number;
  widevineDrmUrl: string;
}

export interface BitrateConfigContent {
  maxBitrate: number;
  maxBitrateVod: number;
  sensitivity: {
    live: string;
    other: string;
  };
}

export interface TechConfigContent {
  hhtechs: Array<HHTech>;
}

export interface HHTech {
  name: HHTechName;
  configuration: HHTechConfiguration;
}

type HHTechName = 'default' | 'njutv' | 'mobtv' | 'flextv';

export interface HHTechConfiguration {
  channels: Channels;
  diagnostics: Diagnostics;
  mainTabs: MainTabs;
  myZone: MyZone;
  npvrActivationPopup: NpvrActivationPopup;
  userInfo: UserInfo;
  vods: Vods;
  withVisibleNpvrFeatures: boolean;
  infoScreen?: InfoScreen;
  usernameWithPinEncryptionRegex: string;
  useOptionalRegionParam: boolean;
  terminals?: Terminals;
}

type ChannelAsset = {
  channelUnavailableWording: AssetContent;
};

interface Channels {
  shouldIndicateHomeZone: boolean;
  assets: Array<ChannelAsset>;
}

interface Diagnostics {
  isHomeZoneConnectionCheckVisible: boolean;
}

interface MainTabs {
  isNpvrTabVisible: boolean;
  isVodTabVisible: boolean;
}

interface MyZone {
  withContinueWatching: boolean;
  withFavoriteVods: boolean;
  withRecommendations: boolean;
  withPromoContent: boolean;
  withRecentlyWatchedChannels: boolean;
}

interface NpvrActivationPopup {
  assets: Array<{
    noNpvrDescription?: AssetContent;
    noNpvrTitle?: AssetContent;
  }>;
}

interface AssetContent {
  content: string;
  contentType: ContentType;
}

export type ContentType = 'wordingVal' | 'wordingKey' | 'image';

interface UserInfo {
  isNpvrVisible: boolean;
  isPurchaseHistoryVisible: boolean;
  isTveSubscriptionInfoVisible: boolean;
}

interface Vods {
  isFavouriteActionVisible: boolean;
  isPurchaseAllowed: boolean;
  assets: Array<VodAsset>;
}

type VodAsset = {
  purchaseDisabled: AssetContent;
};

interface InfoScreen {
  id: string;
  isVisibleToUser: boolean;
}

interface Terminals {
  isBoxlessOTT: boolean;
}

export interface RatingConfigContent {
  randomMaxTimeInMinutes: number;
  randomMinTimeInMinutes: number;
  showToast: boolean;
}

export interface DiagnosticsConfigContent {
  configuration: {
    dnsResolveAddress: string;
    edgeCachesCDNs: Array<{
      name: string;
      url: string;
    }>;
    homeZonePingAddress: string;
  };
}

export interface DefaultConfigContent {
  showUserMsg: boolean;
  userMsg: string;
}
