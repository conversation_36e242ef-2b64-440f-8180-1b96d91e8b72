import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useCallback } from 'react';

import { newApiClient } from 'services/api/newApi';
import { globalConfig } from 'services/config/config';
import { useToasts } from 'services/toasts';
import { Endpoint } from 'services/api/types';
import { isMaxSlotsRemovalExceededError } from 'services/error/guards';
import { useErrorScreen } from 'services/error';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

import { deviceKeys } from './keys';
import { ENDPOINTS } from './endpoints';
import {
  DeviceRegisterRequest,
  DeviceType,
  DeviceUnregisterParams,
  PmsTokenResponse,
  RegisteredTerminalsResponse,
} from './types';

export const useRegisteredTerminalsQuery = (
  shouldBeEnabled: boolean = true,
) => {
  const {
    device: { terminalsMaxLimit },
  } = globalConfig;
  const { getTechConfig } = useConfig();
  const { terminals } = getTechConfig();
  const { isAuthenticated } = useAuthenticationStatus();

  return useQuery(
    deviceKeys.registeredTerminals(),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, RegisteredTerminalsResponse>(
        ENDPOINTS.GET_REGISTERED_TERMINALS,
        {},
        signal,
      );
      return response.data;
    },
    {
      enabled: shouldBeEnabled && isAuthenticated,
      useErrorBoundary: false,
      select: useCallback(
        (data: RegisteredTerminalsResponse) => {
          const { removableDeviceTypes } = globalConfig.device;

          const shouldBeBoxlessHandledAsOTTTerminal = terminals?.isBoxlessOTT;
          shouldBeBoxlessHandledAsOTTTerminal &&
            removableDeviceTypes.push(DeviceType.boxlessAndroidL1);

          const { list, ...rest } = data;
          const noAvailableSlots = terminalsMaxLimit - list.length <= 0;

          const visibleTerminals = list?.filter((terminal) =>
            removableDeviceTypes.includes(terminal.deviceType),
          );

          return { ...rest, list: visibleTerminals, noAvailableSlots };
        },
        [terminals?.isBoxlessOTT, terminalsMaxLimit],
      ),
    },
  );
};

export const useDeviceRegisterMutation = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: DeviceRegisterRequest) => {
      const response = await newApiClient.post<DeviceRegisterRequest, {}, {}>(
        ENDPOINTS.POST_DEVICE_REGISTER,
        data,
      );

      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(deviceKeys.registeredTerminals());
      },
    },
  );
};

export const useDeviceUnregisterMutation = () => {
  const queryClient = useQueryClient();
  const { showErrorModal } = useErrorScreen();
  const { showToast } = useToasts();

  return useMutation(
    async (params: DeviceUnregisterParams) => {
      const { serialNumber } = params;

      const url: Endpoint = {
        ...ENDPOINTS.DELETE_DEVICE_UNREGISTER,
        url: `${ENDPOINTS.DELETE_DEVICE_UNREGISTER.url}?serialNumber=${serialNumber}`,
      };
      const response = await newApiClient.delete(url);
      return response.data;
    },
    {
      onSuccess: async () => {
        showToast('DELETE_TERMINAL');
        await queryClient.invalidateQueries(deviceKeys.registeredTerminals());
      },
      onError: async (error) => {
        if (isMaxSlotsRemovalExceededError(error)) {
          return showErrorModal('UNABLE_TO_DELETE_TERMINAL');
        }
        return showErrorModal('SYSTEM_FAILURE');
      },
    },
  );
};

export const useGetPmsTokenMutation = () => {
  return useMutation(async () => {
    const response = await newApiClient.get<{}, PmsTokenResponse>(
      ENDPOINTS.GET_PMS_TOKEN,
    );
    return response.data.token;
  });
};
