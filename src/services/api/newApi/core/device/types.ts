export enum DeviceType {
  androidMobileOtf = 'android_mobile_otf',
  androidTabletOtf = 'android_tablet_otf',
  iosMobileOtf = 'ios_mobile_otf',
  iosTabletOtf = 'ios_tablet_otf',
  webOtf = 'web_otf',
  androidConnectedTv = 'android_connected_tv',
  androidStb = 'android_stb',
  boxlessAndroidL1 = 'boxless_android_l1',
  stb = 'stb',
  stelvio = 'ConTV_DV9651',
}
export interface Terminal {
  appVer?: string;
  deviceManufacture: string;
  deviceModel: string;
  deviceType: DeviceType;
  macAp?: string;
  macEth?: string;
  macWifi?: string;
  name: string;
  osVer?: string;
  serialNumber: string;
  registrationDate?: string;
}

export interface RegisteredTerminalsResponse {
  list: Array<Terminal>;
}

export interface DeviceRegisterRequest {
  appVer?: string;
  deviceManufacture: string;
  deviceModel: string;
  deviceType: DeviceType;
  macAp?: string;
  macEth?: string;
  macWifi?: string;
  name: string;
  osVer?: string;
  serialNumber: string;
}

export interface DeviceUnregisterParams {
  serialNumber: string;
}

export interface PmsTokenResponse {
  token: string;
}
