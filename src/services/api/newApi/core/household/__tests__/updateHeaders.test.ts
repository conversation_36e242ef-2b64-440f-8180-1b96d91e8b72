import { describe, it, expect, vi } from 'vitest';
import React from 'react';
import { newApiClient } from 'services/api/newApi';
import { setupMockServer } from 'services/api/mock/mock.server';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { useCoreHouseholdQuery } from '../queries';

vi.mock('services/api/oldApi', () => ({
  oldApiClient: {
    updateHeaders: vi.fn(),
  },
}));

vi.mock('services/auth/hooks', () => ({
  useAuthenticationStatus: () => ({
    isAuthenticated: true,
  }),
}));

vi.mock('services/api/newApi', async (importOriginal) => {
  const actual = (await importOriginal()) as any;

  const mockedHeaders = {
    OtvDeviceInfo2: '',
  };

  const mockInstance = {
    defaults: {
      headers: mockedHeaders,
    },
  };

  return {
    ...actual,
    newApiClient: {
      instance: mockInstance,
      get: actual.newApiClient.get,
      updateHeaders: (
        householdExtId?: string,
        hhtech?: string,
        offer?: string,
      ) => {
        const params = new URLSearchParams();
        params.set('hh_tech', hhtech || '');
        params.set('offer', offer || '');
        params.set(
          'serial_number',
          householdExtId ? `UNKNOWN_WEB_TERMINAL_${householdExtId}` : '',
        );

        mockInstance.defaults.headers['OtvDeviceInfo2'] = params.toString();
      },
    },
  };
});

describe('updateHeaders', () => {
  setupMockServer();

  it('should correctly update OtvDeviceInfo2 header after fetch household data', async () => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    const headersBeforeUpdate = newApiClient['instance'].defaults.headers;
    const paramsBeforeUpdate = new URLSearchParams(
      headersBeforeUpdate['OtvDeviceInfo2']?.toString(),
    );
    expect(paramsBeforeUpdate.get('hh_tech')).toEqual(null);
    expect(paramsBeforeUpdate.get('offer')).toEqual(null);
    expect(paramsBeforeUpdate.get('serial_number')).toEqual(null);

    const { result } = renderHook(() => useCoreHouseholdQuery(), {
      wrapper: ({ children }) =>
        React.createElement(
          QueryClientProvider,
          { client: queryClient },
          children,
        ),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });
    newApiClient.updateHeaders('**********', 'ftth', 'offer_from_mock');

    const headersAfterUpdate = newApiClient['instance'].defaults.headers;
    const paramsAfterUpdate = new URLSearchParams(
      headersAfterUpdate['OtvDeviceInfo2']?.toString(),
    );

    expect(paramsAfterUpdate.get('hh_tech')).toEqual('ftth');
    expect(paramsAfterUpdate.get('offer')).toEqual('offer_from_mock');
    expect(paramsAfterUpdate.get('serial_number')).toEqual(
      'UNKNOWN_WEB_TERMINAL_**********',
    );
  });
});
