import { useMutation, useQuery, useQueryClient } from 'react-query';
import { AxiosError } from 'axios';

import {
  HouseholdResponse,
  HouseholdTveServiceResponse,
  LanguageUpdateRequest,
  PinRemainingAttemptsParams,
  PinRemainingAttemptsResponse,
  PinResetRequest,
  PinResetResponse,
  PinUpdateRequest,
  PinUpdateResponse,
  PinVerificationParams,
  PinVerificationResponse,
  SetParentalControlParams,
  SetPurchasePolicyParams,
} from 'services/api/newApi/core/household/types';
import { getUserErrorType, useErrorScreen } from 'services/error';
import { isServerError } from 'services/error/guards';
import { useToasts } from 'services/toasts';
import { generateEncodedPin } from 'utils/cryptography';
import { oldApiClient } from 'services/api/oldApi/client';
import { useAuthenticationStatus } from 'services/user';

import { householdKeys } from './keys';
import { ENDPOINTS } from './endpoints';

import { newApiClient } from '../../client';

export const useCoreHouseholdQuery = () => {
  const { isAuthenticated } = useAuthenticationStatus();

  return useQuery<HouseholdResponse>(
    householdKeys.household(),
    async ({ signal }): Promise<HouseholdResponse> => {
      const response = await newApiClient.get<{}, HouseholdResponse>(
        ENDPOINTS.GET_HOUSEHOLD_HOUSEHOLD,
        {},
        signal,
      );

      const {
        data: { hhTech, offer, householdExtId },
      } = response;

      oldApiClient.updateHeaders(householdExtId, hhTech, offer);
      newApiClient.updateHeaders(householdExtId, hhTech, offer);

      return response.data;
    },
    {
      enabled: isAuthenticated,
      cacheTime: ENDPOINTS.GET_HOUSEHOLD_HOUSEHOLD.RQCacheTime,
      staleTime: ENDPOINTS.GET_HOUSEHOLD_HOUSEHOLD.RQStaleTime,
    },
  );
};

export const useSetHouseholdPurchasePolicyMutation = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToasts();

  return useMutation(
    async (data: SetPurchasePolicyParams) => {
      const response = await newApiClient.post<SetPurchasePolicyParams, {}, {}>(
        ENDPOINTS.SET_PURCHASE_POLICY,
        data,
      );

      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(householdKeys.household());
        showToast('CHANGE_PARENTAL_CONTROL');
      },
      onError: async () => {
        showToast('TRY_AGAIN_ERROR');
      },
    },
  );
};

export const useSetParentalControlMutation = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToasts();

  return useMutation(
    async (data: SetParentalControlParams) => {
      const response = await newApiClient.put<SetParentalControlParams, {}>(
        ENDPOINTS.SET_PARENTAL_CONTROL,
        data,
      );

      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(householdKeys.household());
        showToast('CHANGE_PARENTAL_CONTROL');
      },
      onError: async () => {
        showToast('TRY_AGAIN_ERROR');
      },
    },
  );
};

export const useGetHouseholdMutation = () => {
  return useMutation(async () => {
    const response = await newApiClient.get<{}, HouseholdResponse>(
      ENDPOINTS.GET_HOUSEHOLD_HOUSEHOLD,
    );
    return response.data;
  });
};

export const useSetParentalPinCodeMutation = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: PinUpdateRequest) => {
      const response = await newApiClient.post<
        PinUpdateRequest,
        PinUpdateResponse,
        {}
      >(ENDPOINTS.HOUSEHOLD_PIN_UPDATE, data);
      return response.data;
    },
    {
      onSuccess: async (response) => {
        if (response.status === 'invalidPin') {
          queryClient.setQueryData(
            householdKeys.pinRemainingAttempts({ pinType: 'parentalPin' }),
            () => {
              return { attemptsLeft: response.attemptsLeft };
            },
          );
        }
        if (response.status === 'changed') {
          await queryClient.invalidateQueries(householdKeys.household());
          await queryClient.invalidateQueries(
            householdKeys.pinRemainingAttempts({ pinType: 'parentalPin' }),
          );
        }
      },
    },
  );
};

export const useResetParentalPinCodeMutation = (usePinEncryption: boolean) => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: PinResetRequest) => {
      const secretPinInRightFormat = usePinEncryption
        ? generateEncodedPin(data.secretPin)
        : data.secretPin;

      const response = await newApiClient.post<
        PinResetRequest,
        PinResetResponse,
        {}
      >(ENDPOINTS.HOUSEHOLD_PIN_RESET, {
        ...data,
        secretPin: secretPinInRightFormat,
      });
      return response.data;
    },
    {
      onSuccess: async (response) => {
        if (response.status === 'invalidPin') {
          queryClient.setQueryData(
            householdKeys.pinRemainingAttempts({ pinType: 'secretPin' }),
            () => {
              return { attemptsLeft: response.attemptsLeft };
            },
          );
        }
        if (response.status === 'changed') {
          await queryClient.invalidateQueries(householdKeys.household());
          await queryClient.invalidateQueries(
            householdKeys.pinRemainingAttempts({ pinType: 'secretPin' }),
          );
        }
      },
    },
  );
};

export const useHouseholdTveServiceQuery = (isEnabled: boolean = true) => {
  const { showErrorModal } = useErrorScreen();

  return useQuery(
    householdKeys.householdTveService(),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, HouseholdTveServiceResponse>(
        ENDPOINTS.GET_HOUSEHOLD_TVE_SERVICE,
        {},
        signal,
      );

      return response.data;
    },
    {
      enabled: isEnabled,
      useErrorBoundary: false,
      onError: (error: AxiosError) => {
        if (isServerError(error) || error.response?.status === 412) {
          return;
        }
        showErrorModal(getUserErrorType(error));
      },
    },
  );
};

export const useGetPinRemainingAttemptsQuery = (
  params: PinRemainingAttemptsParams,
) => {
  return useQuery<PinRemainingAttemptsResponse>(
    householdKeys.pinRemainingAttempts(params),
    async ({ signal }): Promise<PinRemainingAttemptsResponse> => {
      const response = await newApiClient.get<
        PinRemainingAttemptsParams,
        PinRemainingAttemptsResponse
      >(ENDPOINTS.GET_PIN_REMAINING_ATTEMPTS, params, signal);

      return response.data;
    },
  );
};

export const useGetPinVerificationMutation = () => {
  return useMutation(
    async (params: PinVerificationParams): Promise<PinVerificationResponse> => {
      const response = await newApiClient.get<
        PinVerificationParams,
        PinVerificationResponse
      >(ENDPOINTS.GET_PIN_VERIFICATION, params);
      return response.data;
    },
  );
};

export const useSetLanguageMutation = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: LanguageUpdateRequest) => {
      const response = await newApiClient.put(ENDPOINTS.SET_LANGUAGE, data);
      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(householdKeys.household());
      },
    },
  );
};
