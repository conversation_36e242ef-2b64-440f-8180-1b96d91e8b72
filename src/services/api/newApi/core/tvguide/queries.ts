import { useMutation, useQuery } from 'react-query';
import { isAxiosError } from 'axios';
import { getUnixTime, subDays } from 'date-fns';

import { useAuthenticationStatus } from 'services/user';
import {
  isNotFoundError,
  isServerError,
  isUnauthorizedError,
} from 'services/error/guards';
import { TimeInMilliseconds, TimeInSeconds } from 'services/api/common/types';

import { ENDPOINTS } from './endpoints';
import {
  ChannelSchedule,
  EpgRequest,
  EpgResponse,
  MediaResponse,
  ProgramDetails,
  SeriesResponse,
} from './types';
import { tvguideKeys } from './keys';

import { newApiClient } from '../../client';

const DEFAULT_IMAGE_PATHS = {
  minih: '',
  miniv: '',
  small: '',
  standard: '',
  thumb: '',
};

export const useTvguideEpgQuery = (params: EpgRequest) => {
  const { isAuthenticated } = useAuthenticationStatus();

  return useQuery(
    tvguideKeys.epg(params),
    async ({ signal }) => {
      try {
        const today: TimeInSeconds = params.date;
        const todayInMilliseconds: TimeInMilliseconds = today * 1000;

        const previousDay: TimeInSeconds = getUnixTime(
          subDays(new Date(todayInMilliseconds), 1),
        );

        const todayResponsePromise = newApiClient.get<EpgRequest, EpgResponse>(
          ENDPOINTS.GET_TVGUIDE_EPG,
          { date: today },
          signal,
        );

        const previousDayResponsePromise = newApiClient.get<
          EpgRequest,
          EpgResponse
        >(ENDPOINTS.GET_TVGUIDE_EPG, { date: previousDay }, signal);

        const [todayResult, previousDayResult] = await Promise.allSettled([
          todayResponsePromise,
          previousDayResponsePromise,
        ]);

        const guideMap: { [key: string]: ChannelSchedule } = {};
        let imagePaths = DEFAULT_IMAGE_PATHS;

        const addToGuideMap = (guideArray: ChannelSchedule[]) => {
          guideArray?.forEach((guide) => {
            if (!guideMap[guide.channelExtId]) {
              guideMap[guide.channelExtId] = {
                ...guide,
                programs: [...guide.programs],
              };
            } else {
              guideMap[guide.channelExtId].programs.push(...guide.programs);
            }
          });
        };

        if (todayResult.status === 'fulfilled') {
          addToGuideMap(todayResult.value.data.guide);
          if (todayResult.value.data.imagePaths) {
            imagePaths = todayResult.value.data.imagePaths;
          }
        } else {
          throw todayResult.reason;
        }

        if (previousDayResult.status === 'fulfilled') {
          addToGuideMap(previousDayResult.value.data.guide);
        } else if (
          isAxiosError(previousDayResult.reason) &&
          previousDayResult.reason.response?.status === 404
        ) {
        } else {
          throw previousDayResult.reason;
        }

        Object.values(guideMap).forEach((guide) => {
          guide.programs.sort((a, b) => a.startTimeUtc - b.startTimeUtc);
        });

        return {
          guide: Object.values(guideMap),
          imagePaths,
        };
      } catch (error: unknown) {
        if (isUnauthorizedError(error) && !isAuthenticated) {
          return {
            guide: [],
            imagePaths: DEFAULT_IMAGE_PATHS,
          };
        }
        // App should works without EPG and with EPG server errors YPT-13463
        if (
          isAxiosError(error) &&
          (isServerError(error) || isNotFoundError(error))
        ) {
          return {
            guide: [],
            imagePaths: DEFAULT_IMAGE_PATHS,
          };
        }

        throw error;
      }
    },
    {
      enabled: Boolean(params.date) && isAuthenticated,
      cacheTime: ENDPOINTS.GET_TVGUIDE_EPG.RQCacheTime,
      staleTime: ENDPOINTS.GET_TVGUIDE_EPG.RQStaleTime,
      useErrorBoundary: false,
    },
  );
};

export const useTvguideProgramQuery = (programExtId: string) => {
  return useQuery(
    tvguideKeys.program({ programExtId }),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, ProgramDetails>(
        {
          ...ENDPOINTS.GET_PROGRAM,
          url: `${ENDPOINTS.GET_PROGRAM.url}?programExtId=${programExtId}`,
        },
        {},
        signal,
      );

      return response.data;
    },
    { enabled: Boolean(programExtId), useErrorBoundary: false },
  );
};

export const useTvguideSeriesQuery = (seriesId: string) => {
  return useQuery(
    tvguideKeys.series({ seriesId }),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, SeriesResponse>(
        {
          ...ENDPOINTS.GET_SERIES,
          url: `${ENDPOINTS.GET_SERIES.url}?seriesId=${seriesId}`,
        },
        {},
        signal,
      );
      return response.data;
    },
    {
      enabled: Boolean(seriesId),
      placeholderData: {} as SeriesResponse,
    },
  );
};

export const useTvguideMediaQuery = (mediaId: string) => {
  return useQuery(
    tvguideKeys.media({ mediaId }),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, MediaResponse>(
        {
          ...ENDPOINTS.GET_MEDIA,
          url: `${ENDPOINTS.GET_MEDIA.url}?mediaId=${mediaId}`,
        },
        {},
        signal,
      );

      if (isAxiosError(response) && response.response?.status === 404) {
        return {
          imagePaths: {
            minih: '',
            miniv: '',
            small: '',
            standard: '',
            thumb: '',
          },
          media: [],
        };
      }

      return response.data;
    },
    { enabled: Boolean(mediaId) },
  );
};

export const useGetProgramParentalControlMutation = () => {
  return useMutation(async (data: { programExtId: string }) => {
    const response = await newApiClient.get<{}, ProgramDetails>({
      ...ENDPOINTS.GET_PROGRAM,
      url: `${ENDPOINTS.GET_PROGRAM.url}?programExtId=${data.programExtId}`,
    });

    return response.data.prLevel;
  });
};
