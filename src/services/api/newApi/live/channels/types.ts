import {
  ChannelId,
  ImagePath,
  ProgramId,
  RecordingId,
  TimeInSeconds,
  TimeShiftingService,
} from 'services/api/common/types';

export interface PlayFeatureDetails {
  isCatchUp: boolean;
  isChannelAllowed: boolean;
  isFastForwardBlocked: boolean;
  isNpvr: boolean;
  isStartOver: boolean;
}
export interface PlayFeatures {
  boxless: PlayFeatureDetails;
  conTv: PlayFeatureDetails;
  otg: PlayFeatureDetails;
  stb: PlayFeatureDetails;
}

export interface Multicast {
  columnFecPort: number;
  fecEnabled: boolean;
  ip: string;
  originalNetworkId: string;
  port: number;
  protocol: string;
  rowFecPort: number;
  serviceId: string;
  transportStreamId: string;
}

export interface Channel {
  catchupDuration: number;
  category: string;
  channelExtId: ChannelId;
  channelNumber: number;
  dai: string;
  extraNumbers: Array<number>;
  frameUrl: string;
  is4K: boolean;
  isAdult: boolean;
  isHomeZoneRestricted: boolean;
  isInteractive: boolean;
  isPayPerView: boolean;
  isRecordingAllowed: boolean;
  isRegionalTv: boolean;
  isSubscribed: boolean;
  logoSignature: string;
  logoImageId: string;
  logoUrl: string;
  multicast: Multicast;
  name: string;
  openWindowId: string;
  playFeatures: PlayFeatures;
}

export interface ChannelListResponse {
  channelList: Array<Channel>;
  channelListLength: number;
  version: string;
  imagePaths?: {
    standard: ImagePath;
  };
}

export interface ChannelPlayInfoParams {
  channelExtId: ChannelId;
  isRegionalTv?: boolean;
  userRegion?: string;
  useOptionalRegionParam?: boolean;
}
export interface ChannelPlayInfoRequest {
  channelExtId: ChannelId;
  isRegionalTv?: boolean;
  userRegion?: string;
  useOptionalRegionParam?: boolean;
}

export interface ChannelPlayInfoResponse {
  casToken: string;
  casTokenExpiration: number;
  duration: TimeInSeconds;
  redirectBypass: boolean;
  resumePosition: TimeInSeconds;
  streamUrl: string;
  streamUrlExpiration: string;
}

export interface ChannelTimeShiftingPlayInfoParams {
  channelExtId: ChannelId;
  programExtId: ProgramId;
  timeShiftingService: TimeShiftingService;
  recordingId?: RecordingId;
}

export interface RefreshLiveSessionQueryParams {
  channelExtId: ChannelId;
  isQueryEnabled: boolean;
}

export interface RefreshLiveSessionMutationParams {
  channelExtId: ChannelId;
}

export interface CloseLiveSessionParams {
  channelExtId: ChannelId;
}

export interface RegionalTvListParams {
  channelExtId: ChannelId;
}

export interface RegionalTv {
  channelName: string;
  extCdnUrlSuffix: string;
  liveUrlSuffix: string;
  multicast: Multicast;
}
export interface RegionalTvPackage {
  channelExtId: string;
  regionalTvList: Array<RegionalTv>;
  version: string;
}

export interface RegionalTvListResponse {
  regionalTvPackageList: Array<RegionalTvPackage>;
}

export interface ManifestResponse {
  result: {
    media_url: string;
  };
}
