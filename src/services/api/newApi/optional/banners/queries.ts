import { useQuery } from 'react-query';
import { useCallback } from 'react';
import { millisecondsToSeconds } from 'date-fns';

import { newApiClient } from 'services/api/newApi';
import { TimeInSeconds } from 'services/api/common/types';
import { getPublicAssetUrl } from 'utils/url';

import { bannerKeys } from './keys';
import { BannersParams, BannersResponse } from './types';
import { ENDPOINTS } from './endpoints';

export const useBannersQuery = (params: BannersParams) => {
  return useQuery<BannersResponse>(
    bannerKeys.banners(params),
    async ({ signal }): Promise<BannersResponse> => {
      const response = await newApiClient.get<{}, BannersResponse>(
        ENDPOINTS.GET_BANNERS,
        { ...params, lineup: 'PRO' },
        signal,
      );

      return response.data;
    },
    {
      useErrorBoundary: false,
      select: useCallback((data: BannersResponse) => {
        const { bannerList, imagePaths, ...rest } = data;
        const activeBanners = bannerList.filter((banner) => {
          const currentTime: TimeInSeconds = millisecondsToSeconds(Date.now());
          const since = banner.startTimestamp || 0;
          const till = banner.endTimestamp || Infinity;
          const isActiveNow = currentTime >= since && currentTime <= till;
          return isActiveNow;
        });

        const bannersWithImage = activeBanners.map((banner) => {
          return {
            ...banner,
            image: imagePaths
              ? getPublicAssetUrl(imagePaths.standard, banner.imageId)
              : '',
          };
        });

        return { ...rest, bannerList: bannersWithImage, imagePaths };
      }, []),
    },
  );
};
