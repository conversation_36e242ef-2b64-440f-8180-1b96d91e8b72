import { ImagePath, TimeInSeconds } from 'services/api/common/types';

export enum LocationType {
  TOP_LINE = 'topLine',
  SECOND_LINE = 'secondLine',
}

export enum BannerActionType {
  VOD_MOVIE = 'vodMovie',
  WWWW = 'www',
  LIVE = 'live',
}

export type BannerAction =
  | { type: BannerActionType.VOD_MOVIE; id: string }
  | { type: BannerActionType.WWWW; url: string }
  | { type: BannerActionType.LIVE; lcn: number };

export interface BannersImagePaths {
  standard: ImagePath;
}

export interface Banner {
  type: LocationType;
  view: string;
  image: string;
  imageId: string;
  startTimestamp: TimeInSeconds;
  endTimestamp: TimeInSeconds;
  action: BannerAction;
}

export interface BannersResponse {
  bannerList: Array<Banner>;
  version: string;
  imagePaths?: BannersImagePaths;
}

export interface BannersParams {
  type: LocationType;
  view: string;
}
