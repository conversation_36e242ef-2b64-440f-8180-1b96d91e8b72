import { useQuery } from 'react-query';

import { getUserErrorType, useErrorScreen } from 'services/error';
import { useAuthenticationStatus } from 'services/user';

import { ENDPOINTS } from './endpoints';
import { billingKeys } from './keys';
import { OrdersRequest, OrdersResponse, PrepaidBalanceResponse } from './types';

import { oldApiClient } from '../client';

export const useOrdersQuery = (params: OrdersRequest) => {
  const { showErrorModal } = useErrorScreen();
  return useQuery(
    billingKeys.orders(),
    async ({ signal }) => {
      const response = await oldApiClient.get<OrdersRequest, OrdersResponse>(
        ENDPOINTS.GET_ORDERS,
        params,
        signal,
      );

      return response.data.orders;
    },
    {
      useErrorBoundary: false,
      onError: (error) => showErrorModal(getUserErrorType(error)),
    },
  );
};

export const usePrepaidBalanceQuery = () => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { showErrorModal } = useErrorScreen();
  return useQuery(
    billingKeys.orders(),
    async ({ signal }) => {
      const response = await oldApiClient.get<{}, PrepaidBalanceResponse>(
        ENDPOINTS.GET_PREPAID_BALANCE,
        {},
        signal,
      );

      return response.data;
    },
    {
      enabled: isAuthenticated,
      useErrorBoundary: false,
      onError: (error) => showErrorModal(getUserErrorType(error)),
    },
  );
};
