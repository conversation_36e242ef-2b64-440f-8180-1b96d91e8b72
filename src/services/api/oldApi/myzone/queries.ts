import { millisecondsToSeconds, secondsToMilliseconds } from 'date-fns';
import { useMutation, useQuery, useQueryClient } from 'react-query';

import { AssetId, ChannelId } from 'services/api/common/types';
import { useToasts } from 'services/toasts';
import { getUserErrorType, useErrorScreen } from 'services/error';
import { Endpoint } from 'services/api/types';
import { useAuthenticationStatus, useUserProfile } from 'services/user';

import { ENDPOINTS } from './endpoints';
import { myzoneKeys } from './keys';
import {
  AddMyListChannel,
  AddMyListVod,
  AddRecentlyWatchedLiveRequest,
  AddRecentlyWatchedRecordingRequest,
  AddRecentlyWatchedVodRequest,
  DeleteRecentlyWatchedChannelRequest,
  DeleteRecentlyWatchedRecordingRequest,
  DeleteRecentlyWatchedVodRequest,
  MyListResponse,
  RecentlyWatchedAllRequest,
  RecentlyWatchedAllResponse,
} from './types';

import { oldApiClient } from '../client';

export const useMyListQuery = () => {
  const { userHhTech } = useUserProfile();
  const { isAuthenticated } = useAuthenticationStatus();

  return useQuery(
    myzoneKeys.myList(),
    async ({ signal }) => {
      const response = await oldApiClient.get<{}, MyListResponse>(
        ENDPOINTS.GET_MY_LIST,
        { hhTech: userHhTech },
        signal,
        'json',
      );

      return response.data;
    },
    {
      staleTime: secondsToMilliseconds(ENDPOINTS.GET_MY_LIST.RQStaleTime),
      cacheTime: secondsToMilliseconds(ENDPOINTS.GET_MY_LIST.RQCacheTime),
      enabled: isAuthenticated,
      useErrorBoundary: false,
    },
  );
};

export const useRecentlyWatchedAllQuery = ({
  dataTypes,
}: RecentlyWatchedAllRequest) => {
  const { userHhTech } = useUserProfile();
  const { isAuthenticated } = useAuthenticationStatus();

  return useQuery(
    myzoneKeys.recentlyWatchedAll(),
    async ({ signal }) => {
      const response = await oldApiClient.get<{}, RecentlyWatchedAllResponse>(
        ENDPOINTS.GET_RECENTLY_WATCHED_ALL,
        { hhTech: userHhTech, dataTypes: dataTypes.join(';') },
        signal,
        'json',
      );

      const currentTime = millisecondsToSeconds(Date.now());

      const programList = response.data.programList
        .filter((program) => program.expirationTimestamp > currentTime)
        .sort(
          (first, next) => next.creationTimestamp - first.creationTimestamp,
        );
      const vodList = response.data.vodList.sort(
        (first, next) => next.creationTimestamp - first.creationTimestamp,
      );
      const recordingList = response.data.recordingList
        .filter((recording) => recording.expirationTimestamp > currentTime)
        .sort(
          (first, next) => next.creationTimestamp - first.creationTimestamp,
        );

      return {
        programList,
        recordingList,
        vodList,
      };
    },
    {
      staleTime: secondsToMilliseconds(
        ENDPOINTS.GET_RECENTLY_WATCHED_ALL.RQStaleTime,
      ),
      cacheTime: secondsToMilliseconds(
        ENDPOINTS.GET_RECENTLY_WATCHED_ALL.RQCacheTime,
      ),
      enabled: isAuthenticated,
      useErrorBoundary: false,
    },
  );
};

export const useMyListMutation = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToasts();

  return useMutation(
    async (data: AddMyListChannel) => {
      const response = await oldApiClient.post(
        ENDPOINTS.POST_MY_LIST_CHANNEL,
        data,
      );

      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.myList());
        showToast('ADD_FAVORITE_CHANNEL');
      },
      onError: () => {
        showToast('ADD_OR_DELETE_FAVORITE_CHANNEL_ERROR');
      },
    },
  );
};

export const useDeleteMyListMutation = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToasts();

  return useMutation(
    async (channelExtId: ChannelId) => {
      const url: Endpoint = {
        ...ENDPOINTS.DELETE_MY_LIST_CHANNEL,
        url: `${ENDPOINTS.DELETE_MY_LIST_CHANNEL.url}/${channelExtId}`,
      };
      const response = await oldApiClient.delete(url);
      return response.data;
    },

    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.myList());
        showToast('DELETE_FAVORITE_CHANNEL');
      },
      onError: () => {
        showToast('ADD_OR_DELETE_FAVORITE_CHANNEL_ERROR');
      },
    },
  );
};

export const useMyListVodMutation = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToasts();

  return useMutation(
    async (data: AddMyListVod) => {
      const response = await oldApiClient.post(
        ENDPOINTS.POST_MY_LIST_VOD,
        data,
      );
      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.myList());
        showToast('ADD_FAVORITE_VOD');
      },
      onError: () => {
        showToast('ADD_OR_DELETE_FAVORITE_VOD_ERROR');
      },
    },
  );
};

export const useDeleteMyListVodMutation = () => {
  const { showToast } = useToasts();
  const queryClient = useQueryClient();

  return useMutation(
    async (vodId: AssetId) => {
      const url: Endpoint = {
        ...ENDPOINTS.DELETE_MY_LIST_VOD,
        url: `${ENDPOINTS.DELETE_MY_LIST_VOD.url}/${vodId}`,
      };
      const response = await oldApiClient.delete(url);
      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.myList());
        showToast('DELETE_FAVORITE_VOD');
      },
      onError: () => {
        showToast('ADD_OR_DELETE_FAVORITE_VOD_ERROR');
      },
    },
  );
};

export const useRecentlyWatchedLiveMutation = () => {
  const { showErrorModal } = useErrorScreen();
  const queryClient = useQueryClient();

  return useMutation(
    async (data: AddRecentlyWatchedLiveRequest) => {
      const response = await oldApiClient.post(
        ENDPOINTS.POST_RECENTLY_WATCHED_LIVE,
        data,
      );

      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.recentlyWatchedAll());
      },
      useErrorBoundary: false,
      onError: (error) => showErrorModal(getUserErrorType(error)),
    },
  );
};

export const useRecentlyWatchedRecordingMutation = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: AddRecentlyWatchedRecordingRequest) => {
      const response = await oldApiClient.post(
        ENDPOINTS.POST_RECENTLY_WATCHED_RECORDING,
        data,
      );

      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.recentlyWatchedAll());
      },
    },
  );
};

export const useRecentlyWatchedVodMutation = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: AddRecentlyWatchedVodRequest) => {
      const response = await oldApiClient.post(
        ENDPOINTS.POST_RECENTLY_WATCHED_VOD,
        data,
      );

      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.recentlyWatchedAll());
      },
    },
  );
};

export const useDeleteRecentlyWatchedChannel = () => {
  const { showToast } = useToasts();
  const queryClient = useQueryClient();

  return useMutation(
    async ({
      channelExtId,
      programExtId,
    }: DeleteRecentlyWatchedChannelRequest) => {
      const url: Endpoint = {
        ...ENDPOINTS.DELETE_RECENTLY_WATCHED_WATCHED_CHANNEL,
        url: `${ENDPOINTS.DELETE_RECENTLY_WATCHED_WATCHED_CHANNEL.url}/${channelExtId}/${programExtId}`,
      };
      const response = await oldApiClient.delete(url);
      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.recentlyWatchedAll());
        showToast('DELETE_CHANNEL_FROM_RECENTLY_WATCHED');
      },
      onError: () => {
        showToast('DELETE_CHANNEL_FROM_RECENTLY_WATCHED_ERROR');
      },
    },
  );
};

export const useDeleteRecentlyWatchedRecording = () => {
  const { showToast } = useToasts();
  const queryClient = useQueryClient();

  return useMutation(
    async ({ otfRecordingId }: DeleteRecentlyWatchedRecordingRequest) => {
      const url: Endpoint = {
        ...ENDPOINTS.DELETE_RECENTLY_WATCHED_RECORDING,
        url: `${ENDPOINTS.DELETE_RECENTLY_WATCHED_RECORDING.url}/${otfRecordingId}`,
      };
      const response = await oldApiClient.delete(url);
      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.recentlyWatchedAll());
        showToast('DELETE_RECORDING_FROM_RECENTLY_WATCHED');
      },
      onError: () => {
        showToast('DELETE_RECORDING_FROM_RECENTLY_WATCHED_ERROR');
      },
    },
  );
};

export const useDeleteRecentlyWatchedVod = () => {
  const { showToast } = useToasts();
  const queryClient = useQueryClient();

  return useMutation(
    async ({ videoExternalId }: DeleteRecentlyWatchedVodRequest) => {
      const url: Endpoint = {
        ...ENDPOINTS.DELETE_RECENTLY_WATCHED_VOD,
        url: `${ENDPOINTS.DELETE_RECENTLY_WATCHED_VOD.url}/${videoExternalId}`,
      };
      const response = await oldApiClient.delete(url);
      return response.data;
    },
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries(myzoneKeys.recentlyWatchedAll());
        showToast('DELETE_VOD_FROM_RECENTLY_WATCHED');
      },
      onError: () => {
        showToast('DELETE_VOD_FROM_RECENTLY_WATCHED_ERROR');
      },
    },
  );
};
