import { useMutation, useQuery, useQueryClient } from 'react-query';
import { millisecondsToSeconds } from 'date-fns';
import { AxiosError } from 'axios';

import { globalConfig } from 'services/config/config';
import { isVodSpecificHandledError } from 'services/toasts/guards';
import { useToasts } from 'services/toasts';
import { Endpoint, ReadEndpoint } from 'services/api/types';
import { capitalizeFirstLetter } from 'utils/text';
import { useAuthenticationStatus } from 'services/user';

import { ENDPOINTS } from './endpoints';
import { vodKeys } from './keys';
import {
  CatalogContentsRequest,
  CloseVodSessionParams,
  CloseVodSessionRequest,
  OrderVodVersionParams,
  OrderVodVersionRequest,
  TrailerPlayInfoRequest,
  TrailerPlayInfoResponse,
  UserPurchasesResponse,
  VodAsset,
  VodAssetRequest,
  VodCatalogContent,
  VodMovieCatalog,
  VodPlayInfoRequest,
  VodPlayInfoResponse,
  VodPurchasesAsset,
} from './types';

import { oldApiClient } from '../client';
import { billingKeys } from '../billing';

export const useUserPurchasesQuery = () => {
  const { isAuthenticated } = useAuthenticationStatus();

  return useQuery(
    vodKeys.userPurchases(),
    async ({ signal }) => {
      const response = await oldApiClient.get<{}, UserPurchasesResponse>(
        ENDPOINTS.GET_USER_PURCHASES,
        signal,
      );

      return response.data;
    },
    {
      enabled: isAuthenticated,
      useErrorBoundary: false,
      select: (userPurchases: UserPurchasesResponse) => {
        return {
          ...userPurchases,
          assetPurchasedList: userPurchases?.assetPurchasedList?.filter(
            (vod) => vod.vodAsset.isAdult !== true,
          ),
        };
      },
    },
  );
};

export const useVodMovieCatalog = () => {
  return useQuery(vodKeys.movieCatalog(), async ({ signal }) => {
    const response = await oldApiClient.get<{}, VodMovieCatalog>(
      ENDPOINTS.GET_VOD_MOVIE_CATALOG,
      signal,
    );

    if (response.data.subDirsStructure) {
      response.data.subDirsStructure = response.data.subDirsStructure.map(
        (subDir) => ({
          ...subDir,
          name: capitalizeFirstLetter(subDir.name),
        }),
      );
    }

    return response.data;
  });
};

export const useVodCatalogContents = (params: CatalogContentsRequest) => {
  return useQuery(vodKeys.catalogContents(params), async ({ signal }) => {
    const response = await oldApiClient.get<{}, VodCatalogContent>(
      ENDPOINTS.GET_VOD_CATALOG_CONTENTS,
      params,
      signal,
    );

    return response.data;
  });
};

export const useVodForYou = () => {
  return useQuery(vodKeys.forYou(), async ({ signal }) => {
    const response = await oldApiClient.get<{}, VodMovieCatalog>(
      ENDPOINTS.GET_VOD_FOR_YOU,
      signal,
    );

    return response.data;
  });
};

export const useVodAsset = (params: VodAssetRequest) => {
  const { assetExternalId } = params;

  const url: Endpoint = {
    ...ENDPOINTS.GET_VOD_ASSET,
    url: `${ENDPOINTS.GET_VOD_ASSET.url}/${assetExternalId}`,
  };

  return useQuery(
    vodKeys.asset(params),
    async ({ signal }) => {
      const response = await oldApiClient.get<{}, VodAsset>(url, signal);

      return response.data;
    },
    { enabled: Boolean(assetExternalId) },
  );
};

export const useGetVodParentalControlMutation = () => {
  return useMutation(async (data: VodAssetRequest) => {
    const url: Endpoint = {
      ...ENDPOINTS.GET_VOD_ASSET,
      url: `${ENDPOINTS.GET_VOD_ASSET.url}/${data.assetExternalId}`,
    };
    const response = await oldApiClient.get<{}, VodAsset>(url);
    return response.data.prLevel;
  });
};

export const useVodPurchasesAsset = (params: VodAssetRequest) => {
  const { assetExternalId } = params;

  const url: Endpoint = {
    ...ENDPOINTS.GET_VOD_PURCHASES_ASSET,
    url: `${ENDPOINTS.GET_VOD_PURCHASES_ASSET.url}/${assetExternalId}`,
  };

  return useQuery(
    vodKeys.purchasesAsset(params),
    async ({ signal }) => {
      const response = await oldApiClient.get<{}, VodPurchasesAsset>(
        url,
        signal,
      );
      return response.data;
    },
    {
      enabled: Boolean(assetExternalId),
      select: (data: VodPurchasesAsset) => {
        const { gifts, ...rest } = data;
        if (gifts) {
          const timeNow = millisecondsToSeconds(new Date().getTime());
          const availableGifts = gifts.filter(
            (gift) => gift.isActive && gift.expirationDate >= timeNow,
          );
          return { ...rest, gifts: availableGifts };
        }
        return data;
      },
    },
  );
};

export const useTrailerPlayInfo = (
  params: TrailerPlayInfoRequest,
  onError?: (error: unknown) => void,
) => {
  const { deviceType } = globalConfig.api;
  const { type, hhTech } = params;

  let url: ReadEndpoint;

  switch (type) {
    case 'asset':
      const { assetExternalId } = params;

      url = {
        ...ENDPOINTS.GET_TRAILER_PLAY_INFO,
        url: `${ENDPOINTS.GET_TRAILER_PLAY_INFO.url}/${assetExternalId}`,
      };
      break;
    case 'version':
      const { versionExternalId } = params;

      url = {
        ...ENDPOINTS.GET_TRAILER_PLAY_INFO_BY_VERSION,
        url: `${ENDPOINTS.GET_TRAILER_PLAY_INFO_BY_VERSION.url}/${versionExternalId}`,
      };
      break;
    default:
      throw Error('Unsupported type for trailer play info query');
  }

  return useQuery(
    vodKeys.trailerPlayInfo(params),
    async ({ signal }) => {
      const response = await oldApiClient.get<{}, TrailerPlayInfoResponse>(
        url,
        { deviceType, hhTech },
        signal,
      );
      return response.data;
    },
    {
      useErrorBoundary: false, // disable errorBoundary to not show app error when trailer query fails
      onError,
    },
  );
};

export const useVodPlayInfo = (
  params: VodPlayInfoRequest,
  onError?: (error: unknown) => void,
) => {
  const { versionExternalId, hhTech } = params;

  const url = {
    ...ENDPOINTS.GET_VOD_PLAY_INFO,
    url: `${ENDPOINTS.GET_VOD_PLAY_INFO.url}/${versionExternalId}`,
  };

  return useQuery(
    vodKeys.vodPlayInfo(params),
    async ({ signal }) => {
      const response = await oldApiClient.get<{}, VodPlayInfoResponse>(
        url,
        { hhTech },
        signal,
      );

      return response.data;
    },
    {
      useErrorBoundary: false,
      onError,
    },
  );
};

export const useCloseVodSessionMutation = (request: CloseVodSessionRequest) => {
  const { versionExternalId } = request;

  const url = {
    ...ENDPOINTS.CLOSE_VOD_SESSION,
    url: `${ENDPOINTS.CLOSE_VOD_SESSION.url}/${versionExternalId}`,
  };

  return useMutation(async (params: CloseVodSessionParams) => {
    const response = await oldApiClient.delete(url, params);

    return response.data;
  });
};

export const useOrderVodVersionMutation = (request: OrderVodVersionRequest) => {
  const queryClient = useQueryClient();
  const { showToast } = useToasts();
  const { versionExternalId } = request;

  const url = {
    ...ENDPOINTS.POST_VOD_PURCHASE_VERSION_ORDER,
    url: `${ENDPOINTS.POST_VOD_PURCHASE_VERSION_ORDER.url}/${versionExternalId}`,
  };

  return useMutation(
    async (params: OrderVodVersionParams) => {
      const response = await oldApiClient.post(url, params);

      return response.data;
    },
    {
      onSuccess: async () => {
        showToast('PURCHASE_VOD');
        await queryClient.invalidateQueries(vodKeys.userPurchases());
        await queryClient.invalidateQueries(
          vodKeys.purchasesAsset({ assetExternalId: versionExternalId }),
        );
        await queryClient.invalidateQueries(billingKeys.all);
      },
      onError: (error: AxiosError) => {
        if (error.response?.status === 412) {
          const {
            response: { data },
          } = error;
          const errorCode = (data as any).errCode || '';

          if (errorCode === 'INSUFFICIENT_CREDIT') {
            return showToast('INSUFFICIENT_CREDIT', 4000);
          }

          if (isVodSpecificHandledError(errorCode)) {
            showToast(errorCode);
            return;
          }
        }
        return showToast('PURCHASE_VOD_ERROR');
      },
    },
  );
};
