import {
  createContext,
  FC,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { Loader } from 'components/Loader';
import {
  BitrateConfigContent,
  ConfigType,
  DefaultConfigContent,
  DiagnosticsConfigContent,
  PlayerConfigContent,
  RatingConfigContent,
  TechConfigContent,
  useAppConfigsQuery,
} from 'services/api/newApi/core/appConfigs';
import { useErrorScreen } from 'services/error';

import { globalConfig } from './config';
import { Config, ConfigContextValue } from './types';
import { useTechsConfig } from './useTechsConfig';

const CONFIG_ENTRIES = [
  { type: ConfigType.PLAYER, revision: 1 },
  { type: ConfigType.BITRATE, revision: 1 },
  { type: ConfigType.DIAGNOSTICS, revision: 1 },
  { type: ConfigType.RATING, revision: 1 },
  { type: ConfigType.TECH_CONFIGURATIONS, revision: 1 },
  { type: ConfigType.DEFAULT, revision: 1 },
];

const ConfigContext = createContext<ConfigContextValue>(
  {} as ConfigContextValue,
);

export const ConfigProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const [config, setConfig] = useState<Config>(globalConfig as Config);

  const { data, isFetching } = useAppConfigsQuery({
    group: 'web',
    types: CONFIG_ENTRIES.map(({ type }) => type),
    revisions: CONFIG_ENTRIES.map(({ revision }) => revision),
  });

  const player = data?.player as PlayerConfigContent;
  const bitrate = data?.bitrate as BitrateConfigContent;
  const diagnosticsConfig = data?.diagnostics as DiagnosticsConfigContent;
  const rating = data?.rating as RatingConfigContent;
  const techsConfig = data?.tech_configurations as TechConfigContent;
  const defaultConfig = data?.default as DefaultConfigContent;

  const techConfig = useTechsConfig(techsConfig);

  const { showErrorPage } = useErrorScreen();

  const appConfig = useMemo((): Config['appConfig'] | undefined => {
    if (player && bitrate && techConfig && rating) {
      return {
        player,
        bitrate,
        diagnosticsConfig,
        rating,
        techConfig,
        defaultConfig,
      };
    }

    return undefined;
  }, [player, bitrate, techConfig, diagnosticsConfig, rating, defaultConfig]);

  useEffect(() => {
    if (!isFetching && appConfig) {
      return setConfig((value) => {
        return {
          ...value,
          appConfig,
        };
      });
    }

    if (!isFetching && !appConfig) {
      showErrorPage('SYSTEM_FAILURE');
    }
  }, [isFetching, appConfig, showErrorPage]);

  // disable app loading until config is ready, disabled when running in vitest testing mode
  if (isFetching && import.meta.env.NODE_ENV !== 'test') {
    return <Loader />;
  }
  const getTechConfig = () => {
    return techConfig;
  };

  return (
    <ConfigContext.Provider value={{ config, getTechConfig }}>
      {children}
    </ConfigContext.Provider>
  );
};

export const useConfig = (): ConfigContextValue => {
  const context = useContext(ConfigContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond ConfigContext');
};
