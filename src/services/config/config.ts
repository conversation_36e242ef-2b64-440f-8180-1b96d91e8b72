import { getDevelopSettingsObj } from 'utils/localStorage/localStorage';
import { DeviceType } from 'services/api/newApi/core/device/types';

import { Config } from './types';

export const PRIVACY_POLICY_EXTERNAL_LINK =
  'https://orange.binaries.pl/bw/20155/0/30529/3dea234d-0a3c-42b8-b174-4e64e4d44ebc';
export const IOS_APP_URL =
  'https://apps.apple.com/pl/app/orange-tv-go/id1061100329';
export const ANDROID_APP_URL =
  'https://play.google.com/store/apps/details?id=com.orange.pl.orangetvgo';

const isDev = Boolean(import.meta.env.VITE_REACT_APP_DEV_MODE);
const countryCode = isDev && getDevelopSettingsObj()?.countryCode;
const minimumWatchingTimeBeforeAddToRecentlyWatched =
  isDev &&
  getDevelopSettingsObj()?.minimumWatchingTimeBeforeAddToRecentlyWatched;

export const globalConfig: Omit<Config, 'appConfig'> = {
  player: {
    licenseKey:
      'ZWEMUyteiTdTSOtA59uGuUgpcbdXmC0WQvUbYjXBO9hbm6jP9uD1SAFCDX7eSYi1wD2LjvKysc3MHrEf36vAdm92Qjh2jjjX4yOYPEpIBUNimt8rAN0F9VfA7vbCk8cIgkv6YwuWA5Eq+oYYqcsaig==',
    drmProdServerCertificateUrl:
      'https://cps.purpledrm.com/wv_certificate/cert_license_widevine_com.bin',
    drmPreProdServerCertificateUrl:
      'https://cps.purpledrm.com/opl/widevine/preprod/cert_uat_widevine_com.bin',
    playerControlsTimeHidden: 4,
    minimumWatchingTimeBeforeAddToRecentlyWatched:
      minimumWatchingTimeBeforeAddToRecentlyWatched
        ? minimumWatchingTimeBeforeAddToRecentlyWatched
        : 60,
  },
  terminal: {
    deviceType: DeviceType.webOtf,
    deviceModel: 'web',
    deviceManufacture: 'web',
    serialNumberType: 'WEB_TERMINAL_1',
  },
  pms: {
    accessType: 'TVE_WEB',
    platform: 'WWW',
  },
  api: {
    deviceType: 'web_otf',
    deviceCat: 'otg',
    countryCode: countryCode ? countryCode : 'pl',
    timeout: 20000,
  },
  epgDatePicker: {
    minDays: 7,
    maxDays: 7,
  },
  device: {
    removableDeviceTypes: [
      DeviceType.androidMobileOtf,
      DeviceType.androidTabletOtf,
      DeviceType.iosMobileOtf,
      DeviceType.iosTabletOtf,
      DeviceType.webOtf,
    ],
    terminalsMaxOTTLimit: 3,
    terminalsMaxLimit: 11,
  },
  profile: {
    defaultAudio: 'pl',
    defaultSubs: 'empty',
    maxPinLength: 4,
  },
  isDev,
  unsupportedTech: { dth: 'dth' },
  restrictedTech: {
    mobtv: 'mobtv',
  },
  unsupportedBrowsers: ['SAFARI'],
  mobileTechnologies: ['mobtv', 'njutv', 'flextv'],
  hiddenNavigationItems: { njutv: 'njutv' },
};
