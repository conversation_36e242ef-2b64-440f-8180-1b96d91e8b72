import { DeviceType } from 'services/api/newApi/core/device/types';
import { AudioOrSubsLanguage } from 'services/api/newApi/core/household';
import {
  BitrateConfigContent,
  DefaultConfigContent,
  DiagnosticsConfigContent,
  HHTechConfiguration,
  PlayerConfigContent,
  RatingConfigContent,
  TechConfigContent,
} from 'services/api/newApi/core/appConfigs';

export interface Config {
  appConfig: {
    player: PlayerConfigContent;
    bitrate: BitrateConfigContent;
    techConfig: HHTechConfiguration;
    diagnosticsConfig?: DiagnosticsConfigContent;
    rating: RatingConfigContent;
    defaultConfig?: DefaultConfigContent;
  };
  player: {
    licenseKey: string;
    drmProdServerCertificateUrl: string;
    drmPreProdServerCertificateUrl: string;
    playerControlsTimeHidden: number;
    minimumWatchingTimeBeforeAddToRecentlyWatched: number;
  };
  terminal: {
    deviceType: DeviceType;
    deviceManufacture: string;
    deviceModel: string;
    serialNumberType: string;
  };
  pms: {
    accessType: 'TVE_WEB';
    platform: 'WWW';
  };
  api: {
    timeout: number;
    deviceCat: string;
    deviceType: string;
    countryCode: string;
  };
  epgDatePicker: {
    minDays: number;
    maxDays: number;
  };
  device: {
    removableDeviceTypes: DeviceType[];
  };
  profile: {
    defaultAudio: AudioOrSubsLanguage;
    defaultSubs: AudioOrSubsLanguage;
    maxPinLength: number;
  };
  isDev: boolean;
  unsupportedTech: {
    dth: string;
  };
  restrictedTech: {
    mobtv: string;
  };
  unsupportedBrowsers: Array<string>;
  mobileTechnologies: Array<string>;
  hiddenNavigationItems: {
    njutv: string;
  };
}

export interface ConfigContextValue {
  config: Config;
  getTechConfig: () => HHTechConfiguration;
}
