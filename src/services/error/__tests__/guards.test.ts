import { AxiosError } from 'axios';
import { isDownForMaintenanceError } from '../guards';

describe('isDownForMaintenanceError', () => {
  it('should return true for new API error G000015', () => {
    const error = {
      isAxiosError: true,
      response: {
        status: 503,
        data: {
          error: 'G000015',
          errCode: 'DOWN_FOR_MAINTENANCE',
          errMsg: 'System maintenance'
        }
      }
    } as AxiosError;

    expect(isDownForMaintenanceError(error)).toBe(true);
  });

  it('should return true for old API error DOWN_FOR_MAINTENANCE', () => {
    const error = {
      isAxiosError: true,
      response: {
        status: 503,
        data: {
          errCode: 'DOWN_FOR_MAINTENANCE',
          errMsg: 'System maintenance'
        }
      }
    } as AxiosError;

    expect(isDownForMaintenanceError(error)).toBe(true);
  });

  it('should return false for different error codes', () => {
    const error = {
      isAxiosError: true,
      response: {
        status: 503,
        data: {
          error: 'G000999',
          errCode: 'OTHER_ERROR',
          errMsg: 'Other error'
        }
      }
    } as AxiosError;

    expect(isDownForMaintenanceError(error)).toBe(false);
  });

  it('should return false for different status codes', () => {
    const error = {
      isAxiosError: true,
      response: {
        status: 500,
        data: {
          error: 'G000015',
          errCode: 'DOWN_FOR_MAINTENANCE',
          errMsg: 'System maintenance'
        }
      }
    } as AxiosError;

    expect(isDownForMaintenanceError(error)).toBe(false);
  });
});
