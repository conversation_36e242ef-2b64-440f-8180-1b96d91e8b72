import { useCallback, useEffect, useRef, useState } from 'react';

import { useLogger } from 'services/logger';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';

import { RecentlyWatchedChannel } from './types';
import { MAXIMUM_NUMBER_OF_SAVED_CHANNELS_PER_PROFILE } from './constants';

import { stores } from '../LocalApiContext/types';
import {
  addItemToDatabaseStore,
  deleteItemFromDatabaseStore,
  getAllItemsFromDatabaseStore,
  updateItemInDatabaseStore,
} from '../LocalApiContext/databaseHelpers';

export const useRecentlyWatchedChannelsHooks = () => {
  const { logger } = useLogger();

  const [recentlyWatchedChannels, setRecentlyWatchedChannels] = useState<
    Array<RecentlyWatchedChannel>
  >([]);
  const isFetching = useRef(false);
  const [isError, setIsError] = useState(false);

  const { data: householdInfo } = useCoreHouseholdQuery();

  const convertAllWatchedChannelsToNewDataModel = useCallback(async () => {
    const watchedChannels =
      await getAllItemsFromDatabaseStore<RecentlyWatchedChannel>(
        stores.channels,
      );
    watchedChannels.forEach(async (channel) => {
      if (channel?.profileId) {
        const { profileId, ...rest } = channel;
        const updatedChannelRecord = {
          ...rest,
          householdExtId: householdInfo?.householdExtId,
        };
        await updateItemInDatabaseStore(updatedChannelRecord, stores.channels);
      }
    });
  }, [householdInfo?.householdExtId]);

  const getRecentlyWatchedChannels = useCallback(async () => {
    try {
      isFetching.current = true;
      setIsError(false);
      const watchedChannels =
        await getAllItemsFromDatabaseStore<RecentlyWatchedChannel>(
          stores.channels,
        );

      return watchedChannels
        .filter(
          (channel) => channel.householdExtId === householdInfo?.householdExtId,
        )
        .sort((firstChannel, secondChannel) =>
          firstChannel.creationTimestamp > secondChannel.creationTimestamp
            ? -1
            : 1,
        );
    } catch (error: unknown) {
      logger.error(`Error while fetching from local api - ${error}`);
      setIsError(true);
      return [];
    } finally {
      isFetching.current = false;
    }
  }, [householdInfo?.householdExtId, logger]);

  const refetchRecentlyWatchedChannels = useCallback(async () => {
    try {
      const channels = await getRecentlyWatchedChannels();
      setRecentlyWatchedChannels(channels);
    } catch (error: unknown) {
      logger.error(
        `Error while refreshing recently watched channels - ${error}`,
      );
    }
  }, [getRecentlyWatchedChannels, logger]);

  const addChannelToRecentlyWatched = useCallback(
    async (channel: RecentlyWatchedChannel) => {
      try {
        setIsError(false);
        const watchedChannels =
          await getAllItemsFromDatabaseStore<RecentlyWatchedChannel>(
            stores.channels,
          );

        const watchedChannelsPerAccount = watchedChannels.filter(
          (watchedChannel) =>
            watchedChannel.householdExtId === householdInfo?.householdExtId,
        );

        const sameChannelExisting = watchedChannelsPerAccount.find(
          (channelFromDatabase) =>
            channelFromDatabase.channelExtId === channel.channelExtId,
        );

        if (sameChannelExisting) {
          await updateItemInDatabaseStore(
            {
              ...sameChannelExisting,
              creationTimestamp: channel.creationTimestamp,
            },
            stores.channels,
          );
          return;
        }

        if (
          watchedChannelsPerAccount.length >=
          MAXIMUM_NUMBER_OF_SAVED_CHANNELS_PER_PROFILE
        ) {
          const oldestRecentlyWatchedChannel = watchedChannelsPerAccount.reduce(
            (previous, current) =>
              current.creationTimestamp < previous.creationTimestamp
                ? current
                : previous,
          );
          await deleteItemFromDatabaseStore(
            oldestRecentlyWatchedChannel.id,
            stores.channels,
          );
        }

        await addItemToDatabaseStore(channel, stores.channels);
      } catch (error: unknown) {
        logger.error(`Error while fetching from local api - ${error}`);
        setIsError(true);
      } finally {
        await refetchRecentlyWatchedChannels();
      }
    },
    [householdInfo?.householdExtId, logger, refetchRecentlyWatchedChannels],
  );

  useEffect(() => {
    let isMounted = true;

    const convertRecentlyWatchedChannels = async () => {
      await convertAllWatchedChannelsToNewDataModel();
      if (isMounted) {
        await refetchRecentlyWatchedChannels();
      }
    };

    if (householdInfo?.householdExtId) {
      convertRecentlyWatchedChannels();
    }
    return () => {
      isMounted = false;
    };
  }, [
    convertAllWatchedChannelsToNewDataModel,
    householdInfo?.householdExtId,
    refetchRecentlyWatchedChannels,
  ]);

  return {
    recentlyWatchedChannels,
    isError,
    isFetching: isFetching.current,
    addChannelToRecentlyWatched,
    getRecentlyWatchedChannels,
    refetchRecentlyWatchedChannels,
  };
};
