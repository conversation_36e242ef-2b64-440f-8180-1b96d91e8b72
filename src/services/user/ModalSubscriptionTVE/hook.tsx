import { useMemo } from 'react';

import {
  useCoreHouseholdQuery,
  useHouseholdTveServiceQuery,
} from 'services/api/newApi/core/household';

import { useUserSession } from '../UserSessionContext';

export const useModalTVESubscription = () => {
  const { data: householdInfo, isFetchedAfterMount: isHouseholdInfoFetched } =
    useCoreHouseholdQuery();
  const { logout } = useUserSession();

  const hasSubscriptionBasedOnHousehold = useMemo(
    () => Boolean(householdInfo?.everywhere),
    [householdInfo?.everywhere],
  );

  const isSubscriptionModalVisible =
    isHouseholdInfoFetched && !hasSubscriptionBasedOnHousehold;

  const shouldFetchTveData = isSubscriptionModalVisible;

  const { data: householdTve } =
    useHouseholdTveServiceQuery(shouldFetchTveData);

  const handleCloseSubscriptionModal = () => {
    logout();
  };

  return {
    isSubscriptionModalVisible,
    canBuy: householdTve?.canBuy,
    handleCloseSubscriptionModal,
  };
};
