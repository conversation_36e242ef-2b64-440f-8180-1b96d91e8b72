import {
  createContext,
  FC,
  PropsWithChildren,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useLocation } from 'react-router-dom';

import { routes } from 'routes/routes-map';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';

import { UserPinActivationValue } from './types';

import { useUserSession } from '../UserSessionContext/UserSessionContext';
import { ModalPinActivation } from '../ModalPinActivation';

export const UserPinActivationContext = createContext<UserPinActivationValue>(
  {} as UserPinActivationValue,
);

export const UserPinActivationProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const location = useLocation();

  const { logout } = useUserSession();

  const { data: householdInfo } = useCoreHouseholdQuery();
  const isParentalPinDefined = householdInfo?.parentalPin === 'set';

  const [isModalPinOpen, setIsModalPinOpen] = useState(false);

  const handleSetPinCode = () => {
    setIsModalPinOpen(false);
  };

  const handleClosePinModal = () => {
    logout();
    setIsModalPinOpen(false);
  };

  useEffect(() => {
    const isWhileLoginProcess = location.pathname === routes.login;
    if (isWhileLoginProcess) {
      return;
    }

    setIsModalPinOpen(Boolean(householdInfo) && isParentalPinDefined === false);
  }, [householdInfo, isParentalPinDefined, location.pathname]);

  return (
    <UserPinActivationContext.Provider
      value={{ isModalPinOpen, setIsModalPinOpen }}
    >
      {isModalPinOpen && (
        <ModalPinActivation
          onSubmit={handleSetPinCode}
          onClose={handleClosePinModal}
        />
      )}
      {children}
    </UserPinActivationContext.Provider>
  );
};

export const useUserPinActivation = (): UserPinActivationValue => {
  const context = useContext(UserPinActivationContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond UserPinActivationContext');
};
