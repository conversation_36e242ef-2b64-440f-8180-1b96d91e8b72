/// <reference types="vite/client" />
/// <reference types="vitest" />
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import browserslistToEsbuild from 'browserslist-to-esbuild';
import type { UserConfig } from 'vite';
import type { InlineConfig } from 'vitest';

import path from 'path';
import fs from 'fs';

type Mode =
  | 'proxy'
  | 'mock'
  | 'prod'
  | 'preprod'
  | 'feature'
  | 'bis'
  | 'dev'
  | 'dev-e2e';

interface VitestConfigExport extends UserConfig {
  test: InlineConfig;
}

const createViteConfig = ({ mode }: { mode: Mode }) => {
  const envConfig = { ...process.env, ...loadEnv(mode, process.cwd(), '') };
  const generateSourceMap = envConfig.GENERATE_SOURCEMAP === 'true';
  const isLocalDevelopment = mode === 'proxy' || mode === 'mock';

  return defineConfig({
    base: '',
    plugins: [
      react({
        babel: {
          plugins: [
            [
              'babel-plugin-styled-components',
              {
                displayName: true,
                fileName: true,
              },
            ],
          ],
        },
      }),
      viteTsconfigPaths(),
    ],
    build: {
      sourcemap: generateSourceMap,
      target: browserslistToEsbuild(['>0.2%', 'not dead', 'not op_mini all']),
    },
    server: isLocalDevelopment
      ? {
          port: 3000,
          open: true,
          proxy: {
            '/gpapi': {
              target: 'https://tvgo.ppd.orange.pl',
              changeOrigin: true,
            },
          },
          https: {
            key: fs.readFileSync(
              path.resolve(__dirname, 'localhost+2-key.pem'),
            ),
            cert: fs.readFileSync(path.resolve(__dirname, 'localhost+2.pem')),
          },
        }
      : undefined,
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/setupTests.ts',
      testTimeout: 20000,
      hookTimeout: 20000,
      css: true,
    },
  } as VitestConfigExport);
};

export default createViteConfig;
